using CarPointCMS.Areas.Admin.Controllers.Base;
using CarPointCMS.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace CarPointCMS.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class DatabaseSeederController : BaseController
    {
        private readonly IDatabaseSeederService _seederService;

        public DatabaseSeederController(IDatabaseSeederService seederService)
        {
            _seederService = seederService;
        }

        public IActionResult Index()
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            return View();
        }

        [HttpPost]
        public async Task<IActionResult> SeedAll()
        {
            if (!IsAdminLoggedIn())
                return Json(new { success = false, message = "Unauthorized" });

            try
            {
                await _seederService.SeedDatabaseAsync();
                return Json(new { success = true, message = "Database seeded successfully!" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Error: {ex.Message}" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SeedCore()
        {
            if (!IsAdminLoggedIn())
                return Json(new { success = false, message = "Unauthorized" });

            try
            {
                await _seederService.SeedCoreDataAsync();
                return Json(new { success = true, message = "Core data seeded successfully!" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Error: {ex.Message}" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SeedContent()
        {
            if (!IsAdminLoggedIn())
                return Json(new { success = false, message = "Unauthorized" });

            try
            {
                await _seederService.SeedContentDataAsync();
                return Json(new { success = true, message = "Content data seeded successfully!" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Error: {ex.Message}" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SeedListings()
        {
            if (!IsAdminLoggedIn())
                return Json(new { success = false, message = "Unauthorized" });

            try
            {
                await _seederService.SeedListingDataAsync();
                return Json(new { success = true, message = "Listing data seeded successfully!" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Error: {ex.Message}" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SeedPackages()
        {
            if (!IsAdminLoggedIn())
                return Json(new { success = false, message = "Unauthorized" });

            try
            {
                await _seederService.SeedPackageDataAsync();
                return Json(new { success = true, message = "Package data seeded successfully!" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Error: {ex.Message}" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ClearAll()
        {
            if (!IsAdminLoggedIn())
                return Json(new { success = false, message = "Unauthorized" });

            try
            {
                await _seederService.ClearAllDataAsync();
                return Json(new { success = true, message = "All data cleared successfully!" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Error: {ex.Message}" });
            }
        }
    }
}
