using CarPointCMS.Models.Entities;
using CarPointCMS.Common;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace CarPointCMS.Data.Seeders
{
    public class DatabaseSeeder
    {
        private readonly ApplicationDbContext _context;

        public DatabaseSeeder(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task SeedAllAsync()
        {
            try
            {
                // Seed in order of dependencies
                await SeedAdminsAsync();
                await SeedUsersAsync();
                await SeedCategoriesAsync();
                await SeedAmenitiesAsync();
                await SeedListingBrandsAsync();
                await SeedListingLocationsAsync();
                await SeedBlogsAsync();
                await SeedFaqsAsync();
                await SeedEmailTemplatesAsync();
                await SeedGeneralSettingsAsync();
                await SeedPackagesAsync();
                await SeedListingsAsync();
                await SeedListingRelatedDataAsync();
                await SeedPackagePurchasesAsync();

                await _context.SaveChangesAsync();
                Console.WriteLine("Database seeding completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during seeding: {ex.Message}");
                throw;
            }
        }

        public async Task SeedAdminsAsync()
        {
            if (await _context.Admins.AnyAsync()) return;

            var admin = new Admin
            {
                Name = "Morshedul Arefin",
                Email = "<EMAIL>",
                Phone = "+8801912721070",
                Country = "Bangladesh",
                Address = "Khulna",
                State = "Khulna",
                City = "Khulna",
                Zip = "9100",
                Website = "https://www.arefindev.com",
                Facebook = "https://www.facebook.com/groups/arefindev",
                LinkedIn = "https://www.linkedin.com/in/arefindev",
                YouTube = "https://www.youtube.com/channel/UCcfYd3bm-e_sLUK29abXb-A",
                Photo = "9ead0b550b7e8cb1fe5351e7893bea6c.jpg",
                Banner = "bb9c875809f781b550ea063b73cda6f0.jpg",
                Password = "$2y$12$.SPOdGyuxg9ImsPwi/4J1Oo4oiDLTT0mcYzaocSIpkRpXJL9AlkKC", // Will be hashed properly
                Token = "",
                Status = AdminStatus.Active,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.Admins.Add(admin);
            await _context.SaveChangesAsync();
        }

        public async Task SeedUsersAsync()
        {
            if (await _context.Users.AnyAsync()) return;

            var users = new List<User>
            {
                new User
                {
                    Name = "Peter Smith",
                    Email = "<EMAIL>",
                    Phone = "************",
                    Country = "USA",
                    Address = "23, PK Road, NYC 45",
                    State = "CA",
                    City = "NYC",
                    Zip = "12982",
                    Website = "https://www.testwebsite.com",
                    Facebook = "https://www.facebook.com/sabbir",
                    Twitter = "https://www.twitter.com/sabbir",
                    Photo = "39953cc10c2b3ca7b26a64a3d53a3527.jpg",
                    Banner = "5913aa1901f13d57607c5308ca4c6ed8.jpg",
                    Password = "$2y$12$IUAfkLUd0SPeIqqxGYtc5OfF./bdRuOaeqr1sTdexByYq5ZFeSYr.", // Will be hashed properly
                    Token = "",
                    Status = UserStatus.Active,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new User
                {
                    Name = "James Hendershot",
                    Email = "<EMAIL>",
                    Phone = "************",
                    Country = "United States",
                    Address = "1240 Tanglewood Road,",
                    State = "MS",
                    City = "Luka",
                    Zip = "38852",
                    Website = "https://www.james101.com",
                    Facebook = "#",
                    Twitter = "#",
                    LinkedIn = "#",
                    Instagram = "#",
                    Pinterest = "#",
                    YouTube = "#",
                    Photo = "9cccdb0438c11135c7cd17549df802fd.jpg",
                    Banner = "faf1e4768e32e6272ef9f7e6342bfa1e.jpg",
                    Password = "$2y$10$jS2sgKkluo1AZR73RPltluFnY3aQF7yxp6INXhfmOn7VrpTEWGp76", // Will be hashed properly
                    Token = "",
                    Status = UserStatus.Active,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new User
                {
                    Name = "Samin Shikder",
                    Email = "<EMAIL>",
                    Phone = "************",
                    Country = "USA",
                    Address = "2642 Rafe Lane",
                    State = "MS",
                    City = "Yazoo City",
                    Zip = "39194",
                    Website = "http://www.samin00.com",
                    Facebook = "#",
                    Twitter = "#",
                    LinkedIn = "#",
                    Instagram = "#",
                    Password = "$2y$10$51K8otGh6RH1CLbwW2YgK.TK0BzI1dJKGdySUA53i2gvDDcMeGruq", // Will be hashed properly
                    Token = "dd46d3a124a85f2f910008ce2c906face1e79a2db1d446bfa8db4843a91f89f5",
                    Status = UserStatus.Active,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new User
                {
                    Name = "Smith Cooper",
                    Email = "<EMAIL>",
                    Password = "$2y$12$jLLmGybPtRkx6fbgUv8ti.blNW2bG.3dyDwjKihgFmlwJ5L9bQR16", // Will be hashed properly
                    Token = "",
                    Status = UserStatus.Active,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            _context.Users.AddRange(users);
            await _context.SaveChangesAsync();
        }

        public async Task SeedCategoriesAsync()
        {
            if (await _context.Categories.AnyAsync()) return;

            var categories = new List<Category>
            {
                new Category
                {
                    CategoryName = "Motor Sport",
                    CategorySlug = "Motor Sport",
                    SeoTitle = "Motor Sport",
                    SeoMetaDescription = "Motor Sport",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Category
                {
                    CategoryName = "Buying Guide",
                    CategorySlug = "buying-guide",
                    SeoTitle = "Buying Guide",
                    SeoMetaDescription = "Buying Guide",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Category
                {
                    CategoryName = "Driving License",
                    CategorySlug = "driving-license",
                    SeoTitle = "Driving License",
                    SeoMetaDescription = "Driving License",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            _context.Categories.AddRange(categories);
            await _context.SaveChangesAsync();
        }

        public async Task SeedAmenitiesAsync()
        {
            if (await _context.Amenities.AnyAsync()) return;

            var amenities = new List<Amenity>
            {
                new Amenity { AmenityName = "Bluetooth", AmenitySlug = "bluetooth", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new Amenity { AmenityName = "Backup Camera", AmenitySlug = "backup-camera", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new Amenity { AmenityName = "Sunroof Availability", AmenitySlug = "sunroof-availability", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new Amenity { AmenityName = "Air Conditioning", AmenitySlug = "air-conditioning", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new Amenity { AmenityName = "USB Ports", AmenitySlug = "usb-ports", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new Amenity { AmenityName = "Speech Voice Recognition", AmenitySlug = "speech-voice-recognition", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new Amenity { AmenityName = "Blind Spot Monitoring", AmenitySlug = "blind-spot-monitoring", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new Amenity { AmenityName = "Cross Traffic Alert", AmenitySlug = "cross-traffic-alert", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new Amenity { AmenityName = "360⁰ View Camera", AmenitySlug = "360-degree-view-camera", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow }
            };

            _context.Amenities.AddRange(amenities);
            await _context.SaveChangesAsync();
        }

        public async Task SeedListingBrandsAsync()
        {
            if (await _context.ListingBrands.AnyAsync()) return;

            var brands = new List<ListingBrand>
            {
                new ListingBrand { ListingBrandName = "Toyota", ListingBrandSlug = "toyota", ListingBrandPhoto = "3fa4918a87203c2b8a4f1f773bc08d74.jpg", SeoTitle = "Toyota", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingBrand { ListingBrandName = "BMW", ListingBrandSlug = "bmw", ListingBrandPhoto = "13d509467d021397f17e943cb68b750c.jpg", SeoTitle = "BMW", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingBrand { ListingBrandName = "Nissan", ListingBrandSlug = "nissan", ListingBrandPhoto = "a4f80e32fdb1db3fa0d0310fe7de62b9.jpg", SeoTitle = "Nissan", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingBrand { ListingBrandName = "Mazda", ListingBrandSlug = "mazda", ListingBrandPhoto = "2bc74c80d17eebacf1de098186e028b0.jpg", SeoTitle = "Mazda", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingBrand { ListingBrandName = "Lamborghini", ListingBrandSlug = "lamborghini", ListingBrandPhoto = "25aab7366ea773e85be7b731e25d5d75.jpg", SeoTitle = "Lamborghini", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingBrand { ListingBrandName = "Volkswagen", ListingBrandSlug = "volkswagen", ListingBrandPhoto = "abf8e2d9e822fc23acd219246c658e2c.jpg", SeoTitle = "Volkswagen", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingBrand { ListingBrandName = "Porsche", ListingBrandSlug = "porsche", ListingBrandPhoto = "bdef15bf04bb381858b82f34216035a3.jpg", SeoTitle = "Porsche", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingBrand { ListingBrandName = "Jaguar", ListingBrandSlug = "jaguar", ListingBrandPhoto = "ecda20d11a98085670055e0935530a27.jpg", SeoTitle = "Jaguar", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow }
            };

            _context.ListingBrands.AddRange(brands);
            await _context.SaveChangesAsync();
        }

        public async Task SeedListingLocationsAsync()
        {
            if (await _context.ListingLocations.AnyAsync()) return;

            var locations = new List<ListingLocation>
            {
                new ListingLocation { ListingLocationName = "San Diego", ListingLocationSlug = "san-diego", ListingLocationPhoto = "eb44f9a13791129efbf73b6557bb6301.jpg", SeoTitle = "San Diego", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingLocation { ListingLocationName = "Chicago", ListingLocationSlug = "chicago", ListingLocationPhoto = "7f2a3cc598783d96c6cb38822f297c4e.jpg", SeoTitle = "Chicago", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingLocation { ListingLocationName = "Los Angeles", ListingLocationSlug = "los-angeles", ListingLocationPhoto = "c9aaf9f4f384a986e92630aee5a729c3.jpg", SeoTitle = "Los Angeles", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingLocation { ListingLocationName = "NewYork", ListingLocationSlug = "newyork", ListingLocationPhoto = "da979a83a7ee727dd73f0fc30bbb4ee0.jpg", SeoTitle = "NewYork", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingLocation { ListingLocationName = "Boston", ListingLocationSlug = "boston", ListingLocationPhoto = "be19037990394911430d0994c988ad15.jpg", SeoTitle = "Boston", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingLocation { ListingLocationName = "Colorado", ListingLocationSlug = "colorado", ListingLocationPhoto = "8c252c3932d5514d03df53ef2bf2f578.jpg", SeoTitle = "Colorado", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingLocation { ListingLocationName = "Portland", ListingLocationSlug = "portland", ListingLocationPhoto = "13ec36fb7431b072dd2d0dedcb9a0327.jpg", SeoTitle = "Portland", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow },
                new ListingLocation { ListingLocationName = "Oakland", ListingLocationSlug = "oakland", ListingLocationPhoto = "76729b551dd233ab8e1e4d4ccfa894d3.jpg", SeoTitle = "Oakland", CreatedAt = DateTime.UtcNow, UpdatedAt = DateTime.UtcNow }
            };

            _context.ListingLocations.AddRange(locations);
            await _context.SaveChangesAsync();
        }

        public async Task SeedBlogsAsync()
        {
            if (await _context.Blogs.AnyAsync()) return;

            var blogs = new List<Blog>
            {
                new Blog
                {
                    CategoryId = 3, // Driving License category
                    PostTitle = "Debitis consequuntur sea eu ex agam",
                    PostSlug = "debitis-consequuntur-sea-eu-ex-agam",
                    PostContent = "<p>Lorem ipsum dolor sit amet, ea qui tation adversarium definitionem, eu labitur denique est. Ad duo quando recusabo petentium. Mea elit affert oportere ex. Ut error affert accusam pri. Sit no causae vidisse invenire, bonorum inermis nec ex.</p>\r\n<p>Eam sint reformidans ex, ex doming iracundia his. Sint modus pro ne, vix ut omnis scripta corpora. Sea graecis suavitate te. Eum tantas possim torquatos ei. An qui falli sadipscing suscipiantur. At congue forensibus constituto his, erat vidit veniam vis eu, dico soleat possim nec ei.</p>",
                    PostContentShort = "Lorem ipsum dolor sit amet, ea qui tation adversarium definitionem, eu labitur denique est. Ad duo quando recusabo petentium.",
                    PostPhoto = "0dad7ff384e8dee92414a9e824423bac.jpg",
                    CommentShow = true,
                    SeoTitle = "Debitis consequuntur sea eu ex agam",
                    SeoMetaDescription = "Debitis consequuntur sea eu ex agam",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Blog
                {
                    CategoryId = 2, // Buying Guide category
                    PostTitle = "An qui falli sadipscing susci piantur at congue",
                    PostSlug = "an-qui-falli-sadipscing-susci-piantur-at-congue",
                    PostContent = "<p>Lorem ipsum dolor sit amet, ea qui tation adversarium definitionem, eu labitur denique est. Ad duo quando recusabo petentium. Mea elit affert oportere ex. Ut error affert accusam pri. Sit no causae vidisse invenire, bonorum inermis nec ex.</p>\r\n<p>Eam sint reformidans ex, ex doming iracundia his. Sint modus pro ne, vix ut omnis scripta corpora. Sea graecis suavitate te. Eum tantas possim torquatos ei. An qui falli sadipscing suscipiantur. At congue forensibus constituto his, erat vidit veniam vis eu, dico soleat possim nec ei.</p>",
                    PostContentShort = "Lorem ipsum dolor sit amet, ea qui tation adversarium definitionem, eu labitur denique est. Ad duo quando recusabo petentium.",
                    PostPhoto = "8fab9f3f622ff6d09db3c031365c3a6a.jpg",
                    CommentShow = true,
                    SeoTitle = "An qui falli sadipscing susci piantur at congue",
                    SeoMetaDescription = "An qui falli sadipscing susci piantur at congue",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Blog
                {
                    CategoryId = 1, // Motor Sport category
                    PostTitle = "Libris impetus molestiae te eu ius ludus",
                    PostSlug = "libris-impetus-molestiae-te-eu-ius-ludus",
                    PostContent = "<p>Lorem ipsum dolor sit amet, ea qui tation adversarium definitionem, eu labitur denique est. Ad duo quando recusabo petentium. Mea elit affert oportere ex. Ut error affert accusam pri. Sit no causae vidisse invenire, bonorum inermis nec ex.</p>\r\n<p>Eam sint reformidans ex, ex doming iracundia his. Sint modus pro ne, vix ut omnis scripta corpora. Sea graecis suavitate te. Eum tantas possim torquatos ei. An qui falli sadipscing suscipiantur. At congue forensibus constituto his, erat vidit veniam vis eu, dico soleat possim nec ei.</p>",
                    PostContentShort = "Lorem ipsum dolor sit amet, ea qui tation adversarium definitionem, eu labitur denique est. Ad duo quando recusabo petentium.",
                    PostPhoto = "86271ff53e1bb67f55fe87d3e2bf5172.jpg",
                    CommentShow = true,
                    SeoTitle = "Libris impetus molestiae te eu ius ludus",
                    SeoMetaDescription = "Libris impetus molestiae te eu ius ludus",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            _context.Blogs.AddRange(blogs);
            await _context.SaveChangesAsync();
        }

        public async Task SeedFaqsAsync()
        {
            if (await _context.Faqs.AnyAsync()) return;

            var faqs = new List<Faq>
            {
                new Faq
                {
                    FaqTitle = "How do I search for cars?",
                    FaqContent = "<p>Lorem ipsum dolor sit amet, eu vim elitr clita, quot putent feugait cu per. Tamquam voluptua persequeris ad cum, at his cibo scaevola. Cibo justo equidem cu nam. An meliore admodum vis, quot aliquip bonorum ei quo. Mea nemore feugiat verterem cu, modus vulputate mea id.</p>",
                    FaqOrder = 1,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Faq
                {
                    FaqTitle = "Where can I find credits in my profile?",
                    FaqContent = "<p>Lorem ipsum dolor sit amet, eu vim elitr clita, quot putent feugait cu per. Tamquam voluptua persequeris ad cum, at his cibo scaevola. Cibo justo equidem cu nam. An meliore admodum vis, quot aliquip bonorum ei quo. Mea nemore feugiat verterem cu, modus vulputate mea id.</p>",
                    FaqOrder = 2,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Faq
                {
                    FaqTitle = "Where are the posting guidelines?",
                    FaqContent = "<p>Lorem ipsum dolor sit amet, eu vim elitr clita, quot putent feugait cu per. Tamquam voluptua persequeris ad cum, at his cibo scaevola. Cibo justo equidem cu nam. An meliore admodum vis, quot aliquip bonorum ei quo. Mea nemore feugiat verterem cu, modus vulputate mea id.</p>",
                    FaqOrder = 3,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Faq
                {
                    FaqTitle = "If I find a car I like, what should I do?",
                    FaqContent = "<p>Lorem ipsum dolor sit amet, eu vim elitr clita, quot putent feugait cu per. Tamquam voluptua persequeris ad cum, at his cibo scaevola. Cibo justo equidem cu nam. An meliore admodum vis, quot aliquip bonorum ei quo. Mea nemore feugiat verterem cu, modus vulputate mea id.</p>",
                    FaqOrder = 4,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            _context.Faqs.AddRange(faqs);
            await _context.SaveChangesAsync();
        }

        public async Task SeedEmailTemplatesAsync()
        {
            if (await _context.EmailTemplates.AnyAsync()) return;

            var emailTemplates = new List<EmailTemplate>
            {
                new EmailTemplate
                {
                    EtSubject = "Contact Form Message",
                    EtContent = "<p>A person has messaged you. Please see it below:&nbsp;<br /><br /><strong>Visitor Message:</strong></p>\r\n<p>Visitor Name: <br />[[visitor_name]]</p>\r\n<p>Visitor Email: <br />[[visitor_email]]</p>\r\n<p>Visitor Phone: <br />[[visitor_phone]]</p>\r\n<p>Visitor Message: <br />[[visitor_message]]</p>",
                    EtName = "Contact Page Message",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new EmailTemplate
                {
                    EtSubject = "New Comment Posted",
                    EtContent = "<p>You have received a new comment from a person. His detail is here:</p><p><strong>Person Name:</strong>&nbsp;[[person_name]]</p><p><strong>Person Email:&nbsp;</strong>[[person_email]]</p><p><strong>Person Message:</strong></p><p>[[person_comment]]</p><p>Go to this link to see this comment<span style=\"font-weight: bolder;\">:&nbsp;</span><a href=\"[[comment_see_url]]\" target=\"_blank\">See Comment</a></p>",
                    EtName = "Comment Message to Admin",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new EmailTemplate
                {
                    EtSubject = "Reset Password",
                    EtContent = "<p>To reset your password, please click on the following link:</p><p><a href=\"[[reset_link]]\" target=\"_blank\">Reset Password</a><br></p>",
                    EtName = "Reset Password Message to Admin",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new EmailTemplate
                {
                    EtSubject = "Confirm Registration",
                    EtContent = "<p>To activate your account and confirm the registration, please click on the verify link below:</p><p><a href=\"[[verification_link]]\" target=\"_blank\">Verification Link</a></p>",
                    EtName = "Registration Email to Customer",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new EmailTemplate
                {
                    EtSubject = "Reset Password",
                    EtContent = "<p>To reset your password, please click on the following link:</p><p><a href=\"[[reset_link]]\" target=\"_blank\">Reset Password Link</a></p>",
                    EtName = "Reset Password Message to Customer",
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            _context.EmailTemplates.AddRange(emailTemplates);
            await _context.SaveChangesAsync();
        }

        public async Task SeedGeneralSettingsAsync()
        {
            if (await _context.GeneralSettings.AnyAsync()) return;

            var generalSettings = new GeneralSettings
            {
                Logo = "a7eb55f8e8c41cbaee0760d15b226f4d.png",
                Favicon = "c1171d7cd44a7f181fb14af3e3545026.png",
                TopPhone = "************",
                TopEmail = "<EMAIL>",
                FooterColumn1Heading = "Categories",
                FooterColumn1TotalItem = 5,
                FooterColumn2Heading = "Locations",
                FooterColumn2TotalItem = 5,
                FooterColumn3Heading = "Footer Menu",
                FooterColumn4Heading = "Contact",
                FooterAddress = "ABC Steet, NewYork.",
                FooterEmail = "<EMAIL>",
                FooterPhone = "************",
                FooterCopyright = "Copyright 2022. ArefinDev. All Rights Reserved.",
                GoogleAnalyticTrackingId = "UA-84213520-6",
                GoogleAnalyticStatus = false,
                TawkLiveChatPropertyId = "60f260f6d6e7610a49abaef3",
                TawkLiveChatStatus = false,
                CookieConsentMessage = "This website uses cookies to ensure you get the best experience on our website.",
                CookieConsentButtonText = "ACCEPT",
                CookieConsentTextColor = "F8FFED",
                CookieConsentBgColor = "50BF20",
                CookieConsentButtonTextColor = "000000",
                CookieConsentButtonBgColor = "FFFFFF",
                CookieConsentStatus = true,
                GoogleRecaptchaSiteKey = "6Lcf1V0bAAAAAIN5nY_O2MXq0hUuTiKTt_XOYM-_",
                GoogleRecaptchaStatus = false,
                ThemeColor = "F6552C",
                CustomerListingOption = true,
                LayoutDirection = "ltr",
                PaypalEnvironment = "sandbox",
                PaypalClientId = "AUepW_R8YYWL7R9nASWIkYSvoLg_3KzYFeb-tt0KMWuWOBwX_JmYlMGKMWbsg_bhPIB2CoNNy5AGk1dm",
                PaypalSecretKey = "EFuwGqxMAPpSMCoxkmo6-WWnt02EjZFNtdN39Z9Ay-rmruF2gR2MmCPdQn1Rk1fH5z93yd96fB5hqP6s",
                PaypalStatus = true,
                StripePublicKey = "pk_test_51JffTyGD31Py00wTwqZ2Sz2y5jULGFtUVnsFkJ3pWhRGECG5gkJpseJx4WAfNcUYXEo0dX6f046rrsRhkawEAikJ00E8QnpubW",
                StripeSecretKey = "sk_test_51JffTyGD31Py00wTTh3LTkKirpsAxsbWGwBONgjMXvPYR8oXI3rXhX9GyEVhndXCVsdJmjruwBlflS1fizakR9Xm00k7am6TAX",
                StripeStatus = true,
                RazorpayKeyId = "rzp_test_fMHrzXegENYrbM",
                RazorpayKeySecret = "18J4uJojTQIItKkYx8scHTlC",
                RazorpayStatus = true,
                FlutterwaveCountry = "NG",
                FlutterwavePublicKey = "FLWPUBK_TEST-30dcb79e11906ad01d5f709580779e94-X",
                FlutterwaveSecretKey = "FLWSECK_TEST-949a9866cf40a09399d047de0d17ca1b-X",
                FlutterwaveStatus = true,
                MollieApiKey = "test_NGxNMkFS89RmD8kh5J7QKP5gtFPn55",
                MollieStatus = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.GeneralSettings.Add(generalSettings);
            await _context.SaveChangesAsync();
        }

        public async Task SeedPackagesAsync()
        {
            if (await _context.Packages.AnyAsync()) return;

            var packages = new List<Package>
            {
                new Package
                {
                    PackageType = "Free",
                    PackageName = "Free",
                    PackagePrice = 0,
                    ValidDays = 1,
                    TotalListings = 1,
                    TotalAmenities = 2,
                    TotalPhotos = 2,
                    TotalVideos = 2,
                    TotalSocialItems = 2,
                    TotalAdditionalFeatures = 2,
                    AllowFeatured = false,
                    PackageOrder = 1,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Package
                {
                    PackageType = "Paid",
                    PackageName = "Standard",
                    PackagePrice = 19,
                    ValidDays = 30,
                    TotalListings = 5,
                    TotalAmenities = 5,
                    TotalPhotos = 5,
                    TotalVideos = 5,
                    TotalSocialItems = 5,
                    TotalAdditionalFeatures = 5,
                    AllowFeatured = true,
                    PackageOrder = 2,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Package
                {
                    PackageType = "Paid",
                    PackageName = "Premium",
                    PackagePrice = 39,
                    ValidDays = 60,
                    TotalListings = 20,
                    TotalAmenities = 20,
                    TotalPhotos = 20,
                    TotalVideos = 20,
                    TotalSocialItems = 20,
                    TotalAdditionalFeatures = 20,
                    AllowFeatured = true,
                    PackageOrder = 3,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            _context.Packages.AddRange(packages);
            await _context.SaveChangesAsync();
        }

        public async Task SeedListingsAsync()
        {
            if (await _context.Listings.AnyAsync()) return;

            var listings = new List<Listing>
            {
                new Listing
                {
                    ListingName = "2022 Toyota 4Runner Limited",
                    ListingSlug = "2022-toyota-4runner-limited",
                    ListingDescription = "<p>Lorem ipsum dolor sit amet, nibh saperet te pri, at nam diceret disputationi. Quo an consul impedit, usu possim evertitur dissentiet ei, ridens minimum nominavi et vix. An per mutat adipisci. Ut pericula dissentias sed, est ea modus gloriatur. Aliquip persius has cu, oportere adversarium mei an, justo fabulas in vix.</p>",
                    ListingAddress = "333 East Broadway Avenue\r\nMaryville, TN 37804",
                    ListingPhone = "(*************",
                    ListingMap = "<iframe src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3237.7024268769424!2d-83.97154938525155!3d35.75811813335977!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x885e9fd3fccccd2b%3A0x4b27a99931ed8fd8!2s333%20E%20Broadway%20Ave%2C%20Maryville%2C%20TN%2037804%2C%20USA!5e0!3m2!1sen!2sbd!4v1625719037629!5m2!1sen!2sbd\" width=\"600\" height=\"450\" style=\"border:0;\" allowfullscreen=\"\" loading=\"lazy\"></iframe>",
                    ListingPrice = "50627",
                    ListingExteriorColor = "Midnight Black Metallic",
                    ListingInteriorColor = "Black Graphite",
                    ListingYear = 2022,
                    ListingMileage = "30000 km",
                    ListingFuelType = "Electric",
                    ListingTransmission = "Automatic",
                    ListingCondition = "New",
                    ListingEngineSize = "1500 cc",
                    ListingCylinder = "4",
                    ListingWheel = "4",
                    ListingBody = "SUV",
                    ListingSeat = 4,
                    ListingModelYear = "2022",
                    ListingEngineCapacity = "1500 cc",
                    ListingDoor = 4,
                    ListingVin = "JHSD8923849",
                    ListingType = "New Car",
                    ListingOhMonday = "9 AM to 5 PM",
                    ListingOhTuesday = "9 AM to 5 PM",
                    ListingOhWednesday = "9 AM to 5 PM",
                    ListingOhThursday = "9 AM to 5 PM",
                    ListingOhFriday = "9 AM to 5 PM",
                    ListingOhSaturday = "9 AM to 5 PM",
                    ListingOhSunday = "9 AM to 5 PM",
                    ListingFeaturedPhoto = "5244eaad7aa66e19f458f166b13366a8.jpg",
                    ListingBrandId = 1, // Toyota
                    ListingLocationId = 3, // Los Angeles
                    UserId = null,
                    AdminId = 1,
                    UserType = "Admin",
                    SeoTitle = "2022 Toyota 4Runner Limited",
                    ListingStatus = ListingStatus.Active,
                    IsFeatured = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Listing
                {
                    ListingName = "2022 Nissan Frontier PRO 4X",
                    ListingSlug = "2022-nissan-frontier-pro-4x",
                    ListingDescription = "<p>Lorem ipsum dolor sit amet, suscipit dissentiunt usu at, eu nam veri vidit signiferumque. Ad mea erat fabellas, et facete everti eum, tation consul ea ius. Autem feugiat maiorum id sea. Est omnis mediocrem assentior ea. Nam ubique possit verterem ea, cum facer scriptorem an.</p>",
                    ListingAddress = "2 Bridge St Old Saybrook, CT 06475",
                    ListingPhone = "(*************",
                    ListingEmail = "<EMAIL>",
                    ListingMap = "<iframe src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2998.1644726412524!2d-72.35277188518384!3d41.283524779273854!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89e62268e8620931%3A0x88b1bc148a89e421!2s2%20Bridge%20St%2C%20Old%20Saybrook%2C%20CT%2006475%2C%20USA!5e0!3m2!1sen!2sbd!4v1625913163080!5m2!1sen!2sbd\" width=\"600\" height=\"450\" style=\"border:0;\" allowfullscreen=\"\" loading=\"lazy\"></iframe>",
                    ListingPrice = "18000",
                    ListingExteriorColor = "Silver",
                    ListingInteriorColor = "Light Black",
                    ListingYear = 2022,
                    ListingMileage = "15000 KM",
                    ListingFuelType = "Hybrid",
                    ListingTransmission = "Automatic",
                    ListingCondition = "New",
                    ListingEngineSize = "2200 CC",
                    ListingCylinder = "6",
                    ListingWheel = "4",
                    ListingBody = "SUV",
                    ListingSeat = 4,
                    ListingModelYear = "2022",
                    ListingEngineCapacity = "2200 CC",
                    ListingDoor = 4,
                    ListingVin = "JHD89238494",
                    ListingType = "New Car",
                    ListingOhMonday = "24 Hours Open",
                    ListingOhTuesday = "24 Hours Open",
                    ListingOhWednesday = "24 Hours Open",
                    ListingOhThursday = "24 Hours Open",
                    ListingOhFriday = "24 Hours Open",
                    ListingOhSaturday = "24 Hours Open",
                    ListingOhSunday = "24 Hours Open",
                    ListingFeaturedPhoto = "34481dd4588dac0ca090e3786c60a8e4.jpg",
                    ListingBrandId = 3, // Nissan
                    ListingLocationId = 3, // Los Angeles
                    UserId = 1, // Peter Smith
                    AdminId = null,
                    UserType = "Customer",
                    SeoTitle = "2022 Nissan Frontier PRO 4X",
                    ListingStatus = ListingStatus.Active,
                    IsFeatured = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            _context.Listings.AddRange(listings);
            await _context.SaveChangesAsync();
        }

        public async Task SeedListingRelatedDataAsync()
        {
            // Seed listing amenities
            var listingAmenities = new List<ListingAmenity>
            {
                new ListingAmenity { ListingId = 1, AmenityId = 1 }, // Toyota - Bluetooth
                new ListingAmenity { ListingId = 1, AmenityId = 2 }, // Toyota - Backup Camera
                new ListingAmenity { ListingId = 1, AmenityId = 7 }, // Toyota - Blind Spot Monitoring
                new ListingAmenity { ListingId = 2, AmenityId = 1 }, // Nissan - Bluetooth
                new ListingAmenity { ListingId = 2, AmenityId = 2 }, // Nissan - Backup Camera
                new ListingAmenity { ListingId = 2, AmenityId = 6 }, // Nissan - Speech Voice Recognition
                new ListingAmenity { ListingId = 2, AmenityId = 7 }, // Nissan - Blind Spot Monitoring
                new ListingAmenity { ListingId = 2, AmenityId = 8 }, // Nissan - Cross Traffic Alert
                new ListingAmenity { ListingId = 2, AmenityId = 9 }  // Nissan - 360⁰ View Camera
            };

            _context.ListingAmenities.AddRange(listingAmenities);

            // Seed listing photos
            var listingPhotos = new List<ListingPhoto>
            {
                new ListingPhoto { ListingId = 1, Photo = "b1cc20004be93df5d0fa9cb85defaae2.jpg" },
                new ListingPhoto { ListingId = 1, Photo = "f264e5614e11102dab514b6208c55711.jpg" },
                new ListingPhoto { ListingId = 1, Photo = "cb6fc696641488cb51604e41e5255d1b.jpg" },
                new ListingPhoto { ListingId = 2, Photo = "7264110e09271723bf7442de251ed3de.jpg" },
                new ListingPhoto { ListingId = 2, Photo = "b97ea802892bae72d3ba0db9f5eda231.jpg" },
                new ListingPhoto { ListingId = 2, Photo = "71c29f7fc069e0330d624fc824bc0029.jpg" }
            };

            _context.ListingPhotos.AddRange(listingPhotos);

            // Seed listing videos
            var listingVideos = new List<ListingVideo>
            {
                new ListingVideo { ListingId = 1, YoutubeVideoId = "8a3klIS-kzw" },
                new ListingVideo { ListingId = 1, YoutubeVideoId = "nVmEIlRyNbc" },
                new ListingVideo { ListingId = 1, YoutubeVideoId = "QoK4VJRsHzc" },
                new ListingVideo { ListingId = 2, YoutubeVideoId = "oSFhdNi-qGc" },
                new ListingVideo { ListingId = 2, YoutubeVideoId = "hySAyK8-JW4" },
                new ListingVideo { ListingId = 2, YoutubeVideoId = "Padg3QDYxjI" }
            };

            _context.ListingVideos.AddRange(listingVideos);

            // Seed listing social items
            var listingSocialItems = new List<ListingSocialItem>
            {
                new ListingSocialItem { ListingId = 1, SocialIcon = "Facebook", SocialUrl = "#" },
                new ListingSocialItem { ListingId = 1, SocialIcon = "Twitter", SocialUrl = "#" },
                new ListingSocialItem { ListingId = 1, SocialIcon = "LinkedIn", SocialUrl = "#" },
                new ListingSocialItem { ListingId = 2, SocialIcon = "Facebook", SocialUrl = "#" },
                new ListingSocialItem { ListingId = 2, SocialIcon = "Twitter", SocialUrl = "#" }
            };

            _context.ListingSocialItems.AddRange(listingSocialItems);

            // Seed listing additional features
            var listingAdditionalFeatures = new List<ListingAdditionalFeature>
            {
                new ListingAdditionalFeature { ListingId = 1, AdditionalFeatureName = "Credit Card Accepted?", AdditionalFeatureValue = "Yes" },
                new ListingAdditionalFeature { ListingId = 2, AdditionalFeatureName = "Credit Card Accepted?", AdditionalFeatureValue = "Yes" },
                new ListingAdditionalFeature { ListingId = 2, AdditionalFeatureName = "Installment Possible?", AdditionalFeatureValue = "No" }
            };

            _context.ListingAdditionalFeatures.AddRange(listingAdditionalFeatures);

            await _context.SaveChangesAsync();
        }

        public async Task SeedPackagePurchasesAsync()
        {
            if (await _context.PackagePurchases.AnyAsync()) return;

            var packagePurchases = new List<PackagePurchase>
            {
                new PackagePurchase
                {
                    UserId = 2, // James Hendershot
                    PackageId = 3, // Premium
                    TransactionId = "PAYID-MDV23AY53140771GK6394933",
                    PaidAmount = 45.00m,
                    PaidCurrency = "USD",
                    PaidCurrencySymbol = "$",
                    AdminAmount = 45,
                    PaymentMethod = "PayPal",
                    PaymentStatus = "Completed",
                    PackageStartDate = DateTime.UtcNow.AddDays(-30),
                    PackageEndDate = DateTime.UtcNow.AddDays(1830), // ~5 years
                    CurrentlyActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new PackagePurchase
                {
                    UserId = 1, // Peter Smith
                    PackageId = 2, // Standard
                    TransactionId = "txn_3Kbz8LGD31Py00wT0xK7nK6I",
                    PaidAmount = 19.00m,
                    PaidCurrency = "USD",
                    PaidCurrencySymbol = "$",
                    AdminAmount = 19,
                    PaymentMethod = "Stripe",
                    PaymentStatus = "Completed",
                    PackageStartDate = DateTime.UtcNow.AddDays(-15),
                    PackageEndDate = DateTime.UtcNow.AddDays(15),
                    CurrentlyActive = false,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            _context.PackagePurchases.AddRange(packagePurchases);
            await _context.SaveChangesAsync();
        }
    }
}
