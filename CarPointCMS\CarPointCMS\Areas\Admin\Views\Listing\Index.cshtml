@using CarPointCMS.Models.Entities
@{
    ViewData["Title"] = "Listing";
}

@model IEnumerable<Listing>

<h1 class="h3 mb-3 text-gray-800">Listing</h1>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
        <div class="float-right d-inline">
            <a asp-area="Admin" asp-controller="Listing" asp-action="Create" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> Add New</a>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Serial</th>
                        <th>Featured Photo</th>
                        <th>Name, Brand, Location</th>
                        <th>Status</th>
                        <th>Is Featured</th>
                        <th class="w_200">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @if (Model != null)
                    {
                        int i = 1;
                        @foreach (var row in Model)
                        {
                            <tr>
                                <td>@i</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(row.ListingFeaturedPhoto))
                                    {
                                        <img src="~/uploads/listing_featured_photos/@row.ListingFeaturedPhoto" alt="" class="w_200">
                                    }
                                    else
                                    {
                                        <img src="~/uploads/listing_featured_photos/default.jpg" alt="" class="w_200">
                                    }
                                </td>
                                <td>
                                    <b>@row.ListingName</b><br>
                                    <small>
                                        @if (row.UserId == null)
                                        {
                                            <b>Added By: Admin</b>
                                        }
                                        else
                                        {
                                            <b>Added By: <a asp-area="Admin" asp-controller="Customer" asp-action="Detail" asp-route-id="@row.UserId" target="_blank">@row.User?.Name</a></b>
                                        }
                                    </small>
                                    <br>
                                    Brand: @(row.ListingBrand?.ListingBrandName ?? "N/A")
                                    <br>
                                    Location: @(row.ListingLocation?.ListingLocationName ?? "N/A")
                                </td>
                                <td>
                                    @if (row.ListingStatus == CarPointCMS.Common.ListingStatus.Active)
                                    {
                                        <a href="javascript:void(0)" onclick="listingStatus(@row.Id)"><input type="checkbox" checked data-toggle="toggle" data-on="Active" data-off="Pending" data-onstyle="success" data-offstyle="danger"></a>
                                    }
                                    else
                                    {
                                        <a href="javascript:void(0)" onclick="listingStatus(@row.Id)"><input type="checkbox" data-toggle="toggle" data-on="Active" data-off="Pending" data-onstyle="success" data-offstyle="danger"></a>
                                    }
                                </td>
                                <td>
                                    @if (row.IsFeatured)
                                    {
                                        <span class="badge badge-success">Yes</span>
                                    }
                                    else
                                    {
                                        <span class="badge badge-danger">No</span>
                                    }
                                </td>
                                <td>
                                    <a href="javascript:void(0)" class="btn btn-success btn-sm" data-toggle="modal" data-target="#detail_info@(row.Id)"><i class="fas fa-eye"></i></a>
                                    <a asp-area="Admin" asp-controller="Listing" asp-action="Edit" asp-route-id="@row.Id" class="btn btn-warning btn-sm"><i class="fas fa-edit"></i></a>
                                    <form asp-area="Admin" asp-controller="Listing" asp-action="Delete" asp-route-id="@row.Id" method="post" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete this listing?');">
                                        <button type="submit" class="btn btn-danger btn-sm"><i class="fas fa-trash-alt"></i></button>
                                    </form>
                                </td>
                            </tr>

                            <!-- Modal -->
                            <div class="modal fade modal_listing_detail" id="detail_info@(row.Id)" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                                <div class="modal-dialog modal-lg" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="exampleModalLabel">Listing Detail</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">

                                            <div class="form-group">
                                                <label for="">Name</label>
                                                <div>@row.ListingName</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="">Slug</label>
                                                <div>@row.ListingSlug</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="">Description</label>
                                                <div>@Html.Raw(row.ListingDescription)</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="">Listing Brand</label>
                                                <div>@row.ListingBrand?.ListingBrandName</div>
                                            </div>

                                            <div class="form-group">
                                                <label for="">Listing Location</label>
                                                <div>@row.ListingLocation?.ListingLocationName</div>
                                            </div>

                                            @if (!string.IsNullOrEmpty(row.ListingAddress))
                                            {
                                                <div class="form-group">
                                                    <label for="">Address</label>
                                                    <div>@Html.Raw(row.ListingAddress?.Replace("\n", "<br>"))</div>
                                                </div>
                                            }

                                            @if (!string.IsNullOrEmpty(row.ListingPhone))
                                            {
                                                <div class="form-group">
                                                    <label for="">Phone</label>
                                                    <div>@Html.Raw(row.ListingPhone?.Replace("\n", "<br>"))</div>
                                                </div>
                                            }

                                            @if (!string.IsNullOrEmpty(row.ListingEmail))
                                            {
                                                <div class="form-group">
                                                    <label for="">Email</label>
                                                    <div>@Html.Raw(row.ListingEmail?.Replace("\n", "<br>"))</div>
                                                </div>
                                            }

                                            @if (!string.IsNullOrEmpty(row.ListingMap))
                                            {
                                                <div class="form-group">
                                                    <label for="">Map</label>
                                                    <div>@Html.Raw(row.ListingMap)</div>
                                                </div>
                                            }

                                            @if (!string.IsNullOrEmpty(row.ListingWebsite))
                                            {
                                                <div class="form-group">
                                                    <label for="">Website</label>
                                                    <div><a href="@row.ListingWebsite" target="_blank">@row.ListingWebsite</a></div>
                                                </div>
                                            }

                                            <div class="form-group">
                                                <label for="">Featured Photo</label>
                                                <div>
                                                    @if (!string.IsNullOrEmpty(row.ListingFeaturedPhoto))
                                                    {
                                                        <img src="~/uploads/listing_featured_photos/@row.ListingFeaturedPhoto" alt="" class="w_200">
                                                    }
                                                    else
                                                    {
                                                        <p>No featured photo uploaded</p>
                                                    }
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label for="">Features</label>

                                                <div class="row bdb bdt">
                                                    <div class="col-md-3"><b>Price</b>:</div>
                                                    <div class="col-md-9">@row.ListingPrice</div>
                                                </div>

                                                <div class="row bdb">
                                                    <div class="col-md-3"><b>Type</b>:</div>
                                                    <div class="col-md-9">@row.ListingType</div>
                                                </div>

                                                @if (!string.IsNullOrEmpty(row.ListingExteriorColor))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Exterior Color</b>:</div>
                                                        <div class="col-md-9">@row.ListingExteriorColor</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingInteriorColor))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Interior Color</b>:</div>
                                                        <div class="col-md-9">@row.ListingInteriorColor</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingFuelType))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Fuel Type</b>:</div>
                                                        <div class="col-md-9">@row.ListingFuelType</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingTransmission))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Transmission</b>:</div>
                                                        <div class="col-md-9">@row.ListingTransmission</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingMileage))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Mileage</b>:</div>
                                                        <div class="col-md-9">@row.ListingMileage</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingModelYear))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Model Year</b>:</div>
                                                        <div class="col-md-9">@row.ListingModelYear</div>
                                                    </div>
                                                }

                                                @if (row.ListingYear.HasValue)
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Year</b>:</div>
                                                        <div class="col-md-9">@row.ListingYear</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingCondition))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Condition</b>:</div>
                                                        <div class="col-md-9">@row.ListingCondition</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingEngineSize))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Engine Size</b>:</div>
                                                        <div class="col-md-9">@row.ListingEngineSize</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingCylinder))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Cylinder</b>:</div>
                                                        <div class="col-md-9">@row.ListingCylinder</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingWheel))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Wheel</b>:</div>
                                                        <div class="col-md-9">@row.ListingWheel</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingBody))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Body</b>:</div>
                                                        <div class="col-md-9">@row.ListingBody</div>
                                                    </div>
                                                }

                                                @if (row.ListingSeat.HasValue)
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Seat</b>:</div>
                                                        <div class="col-md-9">@row.ListingSeat</div>
                                                    </div>
                                                }

                                                @if (row.ListingDoor.HasValue)
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Door</b>:</div>
                                                        <div class="col-md-9">@row.ListingDoor</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingVin))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>VIN</b>:</div>
                                                        <div class="col-md-9">@row.ListingVin</div>
                                                    </div>
                                                }

                                                @if (!string.IsNullOrEmpty(row.ListingEngineCapacity))
                                                {
                                                    <div class="row bdb">
                                                        <div class="col-md-3"><b>Engine Capacity</b>:</div>
                                                        <div class="col-md-9">@row.ListingEngineCapacity</div>
                                                    </div>
                                                }

                                            </div>

                                            @if (!string.IsNullOrEmpty(row.ListingOhMonday) || !string.IsNullOrEmpty(row.ListingOhTuesday) ||
                                                 !string.IsNullOrEmpty(row.ListingOhWednesday) || !string.IsNullOrEmpty(row.ListingOhThursday) ||
                                                 !string.IsNullOrEmpty(row.ListingOhFriday) || !string.IsNullOrEmpty(row.ListingOhSaturday) ||
                                                 !string.IsNullOrEmpty(row.ListingOhSunday))
                                            {
                                                <div class="form-group">
                                                    <label for="">Opening Hours</label>
                                                    @if (!string.IsNullOrEmpty(row.ListingOhMonday))
                                                    {
                                                        <div><b>Monday:</b> @row.ListingOhMonday</div>
                                                    }
                                                    @if (!string.IsNullOrEmpty(row.ListingOhTuesday))
                                                    {
                                                        <div><b>Tuesday:</b> @row.ListingOhTuesday</div>
                                                    }
                                                    @if (!string.IsNullOrEmpty(row.ListingOhWednesday))
                                                    {
                                                        <div><b>Wednesday:</b> @row.ListingOhWednesday</div>
                                                    }
                                                    @if (!string.IsNullOrEmpty(row.ListingOhThursday))
                                                    {
                                                        <div><b>Thursday:</b> @row.ListingOhThursday</div>
                                                    }
                                                    @if (!string.IsNullOrEmpty(row.ListingOhFriday))
                                                    {
                                                        <div><b>Friday:</b> @row.ListingOhFriday</div>
                                                    }
                                                    @if (!string.IsNullOrEmpty(row.ListingOhSaturday))
                                                    {
                                                        <div><b>Saturday:</b> @row.ListingOhSaturday</div>
                                                    }
                                                    @if (!string.IsNullOrEmpty(row.ListingOhSunday))
                                                    {
                                                        <div><b>Sunday:</b> @row.ListingOhSunday</div>
                                                    }
                                                </div>
                                            }

                                            <div class="form-group">
                                                <label for="">Status</label>
                                                <div>
                                                    @if (row.ListingStatus == CarPointCMS.Common.ListingStatus.Active)
                                                    {
                                                        <span class="badge badge-success">Active</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge badge-warning">Pending</span>
                                                    }
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label for="">Featured</label>
                                                <div>
                                                    @if (row.IsFeatured)
                                                    {
                                                        <span class="badge badge-success">Yes</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge badge-secondary">No</span>
                                                    }
                                                </div>
                                            </div>

                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-danger btn-sm" data-dismiss="modal">Close</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- // Modal -->

                            i++;
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function listingStatus(id) {
            $.ajax({
                type: "POST",
                url: "@Url.Action("ChangeStatus", "Listing", new { area = "Admin" })",
                data: { id: id },
                headers: {
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        // Optionally reload the page to reflect changes
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(err) {
                    console.log(err);
                    toastr.error('An error occurred while updating status');
                }
            });
        }
    </script>
}
