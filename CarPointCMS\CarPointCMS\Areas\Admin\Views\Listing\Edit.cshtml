@using CarPointCMS.Models.ViewModels
@{
    ViewData["Title"] = "Edit Listing";
}

@model ListingEditViewModel

<h1 class="h3 mb-3 text-gray-800">Edit Listing</h1>

<form asp-area="Admin" asp-controller="Listing" asp-action="Edit" asp-route-id="@Model.Id" method="post" enctype="multipart/form-data">
    <input type="hidden" name="CurrentPhoto" value="@(Model.ExistingFeaturedPhoto ?? "")">

    <div class="card shadow mb-4 t-left">
        <div class="card-header py-3">
            <h6 class="m-0 mt-2 font-weight-bold text-primary"></h6>
            <div class="float-right d-inline">
                <a asp-area="Admin" asp-controller="Listing" asp-action="Index" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> View All</a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-3">
                    <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <a class="nav-link active" id="p1_tab" data-toggle="pill" href="#p1" role="tab" aria-controls="p1" aria-selected="true">Main Section</a>
                        <a class="nav-link" id="p10_tab" data-toggle="pill" href="#p10" role="tab" aria-controls="p10" aria-selected="false">Features</a>
                        <a class="nav-link" id="p2_tab" data-toggle="pill" href="#p2" role="tab" aria-controls="p2" aria-selected="false">Opening Hour</a>
                        <a class="nav-link" id="p3_tab" data-toggle="pill" href="#p3" role="tab" aria-controls="p3" aria-selected="false">Social Media</a>
                        <a class="nav-link" id="p4_tab" data-toggle="pill" href="#p4" role="tab" aria-controls="p4" aria-selected="false">Amenity</a>
                        <a class="nav-link" id="p5_tab" data-toggle="pill" href="#p5" role="tab" aria-controls="p5" aria-selected="false">Photo Gallery</a>
                        <a class="nav-link" id="p6_tab" data-toggle="pill" href="#p6" role="tab" aria-controls="p6" aria-selected="false">Video Gallery</a>
                        <a class="nav-link" id="p7_tab" data-toggle="pill" href="#p7" role="tab" aria-controls="p7" aria-selected="false">Additional Features</a>
                        <a class="nav-link" id="p8_tab" data-toggle="pill" href="#p8" role="tab" aria-controls="p8" aria-selected="false">SEO</a>
                        <a class="nav-link" id="p9_tab" data-toggle="pill" href="#p9" role="tab" aria-controls="p9" aria-selected="false">Status and Featured</a>
                    </div>
                </div>
                <div class="col-9">
                    <div class="tab-content" id="v-pills-tabContent">

                        <!-- Tab 1: Main Section -->
                        <div class="tab-pane fade show active" id="p1" role="tabpanel" aria-labelledby="p1_tab">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Name *</label>
                                        <input type="text" asp-for="ListingName" class="form-control" autofocus>
                                        <span asp-validation-for="ListingName" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Slug</label>
                                        <input type="text" asp-for="ListingSlug" class="form-control">
                                        <span asp-validation-for="ListingSlug" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="">Description *</label>
                                <textarea asp-for="ListingDescription" class="form-control editor" cols="30" rows="10"></textarea>
                                <span asp-validation-for="ListingDescription" class="text-danger"></span>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Listing Brand</label>
                                        <select asp-for="ListingBrandId" asp-items="Model.Brands" class="form-control select2">
                                            <option value="">Select Brand</option>
                                        </select>
                                        <span asp-validation-for="ListingBrandId" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Listing Location</label>
                                        <select asp-for="ListingLocationId" asp-items="Model.Locations" class="form-control select2">
                                            <option value="">Select Location</option>
                                        </select>
                                        <span asp-validation-for="ListingLocationId" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Address</label>
                                        <textarea asp-for="ListingAddress" class="form-control h_70" cols="30" rows="10"></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Phone</label>
                                        <textarea asp-for="ListingPhone" class="form-control h_70" cols="30" rows="10"></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Email</label>
                                        <textarea asp-for="ListingEmail" class="form-control h_70" cols="30" rows="10"></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Map Iframe Code</label>
                                        <textarea asp-for="ListingMap" class="form-control h_70" cols="30" rows="10"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="">Website</label>
                                <input type="text" asp-for="ListingWebsite" class="form-control">
                            </div>

                            <div class="form-group">
                                <label for="">Existing Featured Photo</label>
                                <div>
                                    @if (!string.IsNullOrEmpty(Model.ExistingFeaturedPhoto))
                                    {
                                        <img src="~/uploads/listing_featured_photos/@Model.ExistingFeaturedPhoto" class="w_200" alt="">
                                    }
                                    else
                                    {
                                        <p>No featured photo uploaded</p>
                                    }
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="">Change Featured Photo</label>
                                <div>
                                    <input type="file" asp-for="ListingFeaturedPhoto">
                                </div>
                            </div>
                        </div>

                        <!-- Tab 10: Features -->
                        <div class="tab-pane fade" id="p10" role="tabpanel" aria-labelledby="p10_tab">
                            <h4 class="heading-in-tab">Features</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Price *</label>
                                        <input type="text" asp-for="ListingPrice" class="form-control">
                                        <span asp-validation-for="ListingPrice" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Type</label>
                                        <select asp-for="ListingType" class="form-control">
                                            <option value="New Car">New Car</option>
                                            <option value="Used Car">Used Car</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Exterior Color</label>
                                        <input type="text" asp-for="ListingExteriorColor" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Interior Color</label>
                                        <input type="text" asp-for="ListingInteriorColor" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Cylinder</label>
                                        <input type="text" asp-for="ListingCylinder" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Fuel Type</label>
                                        <input type="text" asp-for="ListingFuelType" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Transmission</label>
                                        <input type="text" asp-for="ListingTransmission" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Engine Capacity</label>
                                        <input type="text" asp-for="ListingEngineCapacity" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">VIN</label>
                                        <input type="text" asp-for="ListingVin" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Body</label>
                                        <input type="text" asp-for="ListingBody" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Seat</label>
                                        <input type="text" asp-for="ListingSeat" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Wheel</label>
                                        <input type="text" asp-for="ListingWheel" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Door</label>
                                        <input type="text" asp-for="ListingDoor" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Mileage</label>
                                        <input type="text" asp-for="ListingMileage" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Model Year</label>
                                        <input type="text" asp-for="ListingModelYear" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Year</label>
                                        <input type="text" asp-for="ListingYear" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Condition</label>
                                        <input type="text" asp-for="ListingCondition" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Engine Size</label>
                                        <input type="text" asp-for="ListingEngineSize" class="form-control">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 2: Opening Hours -->
                        <div class="tab-pane fade" id="p2" role="tabpanel" aria-labelledby="p2_tab">
                            <h4 class="heading-in-tab">Opening Hour</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Monday</label>
                                        <input type="text" asp-for="ListingOhMonday" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Tuesday</label>
                                        <input type="text" asp-for="ListingOhTuesday" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Wednesday</label>
                                        <input type="text" asp-for="ListingOhWednesday" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Thursday</label>
                                        <input type="text" asp-for="ListingOhThursday" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Friday</label>
                                        <input type="text" asp-for="ListingOhFriday" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Saturday</label>
                                        <input type="text" asp-for="ListingOhSaturday" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="">Sunday</label>
                                        <input type="text" asp-for="ListingOhSunday" class="form-control">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 3: Social Media -->
                        <div class="tab-pane fade" id="p3" role="tabpanel" aria-labelledby="p3_tab">
                            <h4 class="heading-in-tab">Existing Social Media</h4>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            @if (Model.ExistingSocialItems.Any())
                                            {
                                                @foreach (var socialItem in Model.ExistingSocialItems)
                                                {
                                                    <tr>
                                                        <td>@socialItem.SocialIcon</td>
                                                        <td>@socialItem.SocialUrl</td>
                                                        <td>
                                                            <a href="javascript:void(0)" onclick="deleteSocialItem(@socialItem.Id)" class="btn btn-danger btn-sm">Delete</a>
                                                        </td>
                                                    </tr>
                                                }
                                            }
                                            else
                                            {
                                                <tr>
                                                    <td colspan="3">No existing social media items</td>
                                                </tr>
                                            }
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <h4 class="heading-in-tab mt_30">New Social Media</h4>
                            <div class="social_item">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <select name="SocialIcon[]" class="form-control">
                                                <option value="Facebook">Facebook</option>
                                                <option value="Twitter">Twitter</option>
                                                <option value="LinkedIn">LinkedIn</option>
                                                <option value="YouTube">YouTube</option>
                                                <option value="Pinterest">Pinterest</option>
                                                <option value="GooglePlus">Google Plus</option>
                                                <option value="Instagram">Instagram</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <input type="text" name="SocialUrl[]" class="form-control" placeholder="URL">
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="btn btn-success add_social_more"><i class="fas fa-plus"></i></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 4: Amenity -->
                        <div class="tab-pane fade" id="p4" role="tabpanel" aria-labelledby="p4_tab">
                            <h4 class="heading-in-tab">Amenity</h4>
                            <div class="row">
                                @foreach (var amenity in Model.Amenities)
                                {
                                    <div class="col-md-4">
                                        <div class="form-check mb_10">
                                            <input class="form-check-input" name="SelectedAmenities" type="checkbox" value="@amenity.Value" id="<EMAIL>" @(amenity.Selected ? "checked" : "")>
                                            <label class="form-check-label" for="<EMAIL>">
                                                @amenity.Text
                                            </label>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>

                        <!-- Tab 5: Photo Gallery -->
                        <div class="tab-pane fade" id="p5" role="tabpanel" aria-labelledby="p5_tab">
                            <h4 class="heading-in-tab">Existing Photos</h4>
                            <div class="row">
                                @if (Model.ExistingPhotos.Any())
                                {
                                    @foreach (var photo in Model.ExistingPhotos)
                                    {
                                        <div class="col-md-3 mb-3">
                                            <img src="~/uploads/listing_photos/@photo.Photo" class="img-thumbnail" alt="">
                                            <div class="mt-2">
                                                <a href="javascript:void(0)" onclick="deletePhoto(@photo.Id)" class="btn btn-danger btn-sm">Delete</a>
                                            </div>
                                        </div>
                                    }
                                }
                                else
                                {
                                    <div class="col-md-12">
                                        <p>No existing photos</p>
                                    </div>
                                }
                            </div>

                            <h4 class="heading-in-tab mt_30">New Photos</h4>
                            <div class="photo_item">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <div>
                                                <input type="file" name="AdditionalPhotos" multiple>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="btn btn-success add_photo_more"><i class="fas fa-plus"></i></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 6: Video Gallery -->
                        <div class="tab-pane fade" id="p6" role="tabpanel" aria-labelledby="p6_tab">
                            <h4 class="heading-in-tab">Existing Videos</h4>
                            <div class="row">
                                @if (Model.ExistingVideos.Any())
                                {
                                    @foreach (var video in Model.ExistingVideos)
                                    {
                                        <div class="col-md-4 mb-3">
                                            <div class="card">
                                                <div class="card-body">
                                                    <p><strong>YouTube ID:</strong> @video.YoutubeVideoId</p>
                                                    <a href="javascript:void(0)" onclick="deleteVideo(@video.Id)" class="btn btn-danger btn-sm">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                }
                                else
                                {
                                    <div class="col-md-12">
                                        <p>No existing videos</p>
                                    </div>
                                }
                            </div>

                            <h4 class="heading-in-tab mt_30">New Videos</h4>
                            <div class="video_item">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <input type="text" name="YoutubeVideoIds" class="form-control" placeholder="YouTube Video ID">
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="btn btn-success add_video_more"><i class="fas fa-plus"></i></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 7: Additional Features -->
                        <div class="tab-pane fade" id="p7" role="tabpanel" aria-labelledby="p7_tab">
                            <h4 class="heading-in-tab">Existing Additional Features</h4>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            @if (Model.ExistingAdditionalFeatures.Any())
                                            {
                                                @foreach (var feature in Model.ExistingAdditionalFeatures)
                                                {
                                                    <tr>
                                                        <td>@feature.AdditionalFeatureName</td>
                                                        <td>@feature.AdditionalFeatureValue</td>
                                                        <td>
                                                            <a href="javascript:void(0)" onclick="deleteAdditionalFeature(@feature.Id)" class="btn btn-danger btn-sm">Delete</a>
                                                        </td>
                                                    </tr>
                                                }
                                            }
                                            else
                                            {
                                                <tr>
                                                    <td colspan="3">No existing additional features</td>
                                                </tr>
                                            }
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <h4 class="heading-in-tab mt_30">New Additional Features</h4>
                            <div class="additional_feature_item">
                                <div class="row">
                                    <div class="col-md-5">
                                        <div class="form-group">
                                            <input type="text" name="AdditionalFeatureNames" class="form-control" placeholder="Name">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <input type="text" name="AdditionalFeatureValues" class="form-control" placeholder="Value">
                                        </div>
                                    </div>
                                    <div class="col-md-1">
                                        <div class="btn btn-success add_additional_feature_more"><i class="fas fa-plus"></i></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 8: SEO -->
                        <div class="tab-pane fade" id="p8" role="tabpanel" aria-labelledby="p8_tab">
                            <div class="form-group">
                                <label for="">Title</label>
                                <input type="text" asp-for="SeoTitle" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="">Meta Description</label>
                                <textarea asp-for="SeoMetaDescription" class="form-control h_100" cols="30" rows="10"></textarea>
                            </div>
                        </div>

                        <!-- Tab 9: Status and Featured -->
                        <div class="tab-pane fade" id="p9" role="tabpanel" aria-labelledby="p9_tab">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="">Status</label>
                                        <select asp-for="ListingStatus" class="form-control">
                                            <option value="Active">Active</option>
                                            <option value="Pending">Pending</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="">Is Featured?</label>
                                        <select asp-for="IsFeatured" class="form-control">
                                            <option value="true">Yes</option>
                                            <option value="false">No</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
    <button type="submit" class="btn btn-success btn-block mb_40">Update</button>
</form>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Add more social media items
            $('.add_social_more').click(function() {
                var html = '<div class="row mt-2">' +
                    '<div class="col-md-5">' +
                    '<div class="form-group">' +
                    '<select name="SocialIcons" class="form-control">' +
                    '<option value="Facebook">Facebook</option>' +
                    '<option value="Twitter">Twitter</option>' +
                    '<option value="LinkedIn">LinkedIn</option>' +
                    '<option value="YouTube">YouTube</option>' +
                    '<option value="Pinterest">Pinterest</option>' +
                    '<option value="GooglePlus">Google Plus</option>' +
                    '<option value="Instagram">Instagram</option>' +
                    '</select>' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-md-6">' +
                    '<div class="form-group">' +
                    '<input type="text" name="SocialUrls" class="form-control" placeholder="URL">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-md-1">' +
                    '<div class="btn btn-danger remove_social"><i class="fas fa-minus"></i></div>' +
                    '</div>' +
                    '</div>';
                $('.social_item').append(html);
            });

            // Remove social media item
            $(document).on('click', '.remove_social', function() {
                $(this).closest('.row').remove();
            });

            // Add more photos
            $('.add_photo_more').click(function() {
                var html = '<div class="row mt-2">' +
                    '<div class="col-md-5">' +
                    '<div class="form-group">' +
                    '<input type="file" name="AdditionalPhotos" multiple>' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-md-1">' +
                    '<div class="btn btn-danger remove_photo"><i class="fas fa-minus"></i></div>' +
                    '</div>' +
                    '</div>';
                $('.photo_item').append(html);
            });

            // Remove photo item
            $(document).on('click', '.remove_photo', function() {
                $(this).closest('.row').remove();
            });

            // Add more videos
            $('.add_video_more').click(function() {
                var html = '<div class="row mt-2">' +
                    '<div class="col-md-5">' +
                    '<div class="form-group">' +
                    '<input type="text" name="YoutubeVideoIds" class="form-control" placeholder="YouTube Video ID">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-md-1">' +
                    '<div class="btn btn-danger remove_video"><i class="fas fa-minus"></i></div>' +
                    '</div>' +
                    '</div>';
                $('.video_item').append(html);
            });

            // Remove video item
            $(document).on('click', '.remove_video', function() {
                $(this).closest('.row').remove();
            });

            // Add more additional features
            $('.add_additional_feature_more').click(function() {
                var html = '<div class="row mt-2">' +
                    '<div class="col-md-5">' +
                    '<div class="form-group">' +
                    '<input type="text" name="AdditionalFeatureNames" class="form-control" placeholder="Name">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-md-6">' +
                    '<div class="form-group">' +
                    '<input type="text" name="AdditionalFeatureValues" class="form-control" placeholder="Value">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-md-1">' +
                    '<div class="btn btn-danger remove_additional_feature"><i class="fas fa-minus"></i></div>' +
                    '</div>' +
                    '</div>';
                $('.additional_feature_item').append(html);
            });

            // Remove additional feature item
            $(document).on('click', '.remove_additional_feature', function() {
                $(this).closest('.row').remove();
            });
        });

        // Delete functions for existing items
        function deleteSocialItem(id) {
            if (confirm('Are you sure you want to delete this social media item?')) {
                $.post('@Url.Action("DeleteSocialItem", "Listing", new { area = "Admin" })', { id: id })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + response.message);
                        }
                    })
                    .fail(function() {
                        alert('An error occurred while deleting the social media item.');
                    });
            }
        }

        function deletePhoto(id) {
            if (confirm('Are you sure you want to delete this photo?')) {
                $.post('@Url.Action("DeletePhoto", "Listing", new { area = "Admin" })', { id: id })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + response.message);
                        }
                    })
                    .fail(function() {
                        alert('An error occurred while deleting the photo.');
                    });
            }
        }

        function deleteVideo(id) {
            if (confirm('Are you sure you want to delete this video?')) {
                $.post('@Url.Action("DeleteVideo", "Listing", new { area = "Admin" })', { id: id })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + response.message);
                        }
                    })
                    .fail(function() {
                        alert('An error occurred while deleting the video.');
                    });
            }
        }

        function deleteAdditionalFeature(id) {
            if (confirm('Are you sure you want to delete this additional feature?')) {
                $.post('@Url.Action("DeleteAdditionalFeature", "Listing", new { area = "Admin" })', { id: id })
                    .done(function(response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert('Error: ' + response.message);
                        }
                    })
                    .fail(function() {
                        alert('An error occurred while deleting the additional feature.');
                    });
            }
        }
    </script>
}
