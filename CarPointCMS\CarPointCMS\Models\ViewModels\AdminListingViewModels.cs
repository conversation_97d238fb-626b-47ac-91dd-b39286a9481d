using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;
using CarPointCMS.Models.Entities;

namespace CarPointCMS.Models.ViewModels
{
    public class ListingCreateViewModel
{
    [Required(ErrorMessage = "Listing name is required")]
    [StringLength(255, ErrorMessage = "Listing name cannot exceed 255 characters")]
    public string ListingName { get; set; } = "";

    [StringLength(255, ErrorMessage = "Listing slug cannot exceed 255 characters")]
    public string? ListingSlug { get; set; }

    [Required(ErrorMessage = "Listing description is required")]
    public string ListingDescription { get; set; } = "";

    [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
    public string? ListingAddress { get; set; }

    [StringLength(20, ErrorMessage = "Phone cannot exceed 20 characters")]
    public string? ListingPhone { get; set; }

    [StringLength(255, ErrorMessage = "Email cannot exceed 255 characters")]
    public string? ListingEmail { get; set; }

    [StringLength(255, ErrorMessage = "Website cannot exceed 255 characters")]
    public string? ListingWebsite { get; set; }

    public string? ListingMap { get; set; }

    [Required(ErrorMessage = "Price is required")]
    [Range(0, double.MaxValue, ErrorMessage = "Price must be a positive number")]
    public decimal ListingPrice { get; set; }

    [StringLength(50, ErrorMessage = "Exterior color cannot exceed 50 characters")]
    public string? ListingExteriorColor { get; set; }

    [StringLength(50, ErrorMessage = "Interior color cannot exceed 50 characters")]
    public string? ListingInteriorColor { get; set; }

    [Range(1, 20, ErrorMessage = "Cylinder count must be between 1 and 20")]
    public int? ListingCylinder { get; set; }


    [StringLength(50, ErrorMessage = "Fuel type cannot exceed 50 characters")]
    public string? ListingFuelType { get; set; }

    [StringLength(50, ErrorMessage = "Transmission cannot exceed 50 characters")]
    public string? ListingTransmission { get; set; }
    [StringLength(50, ErrorMessage = "Engine capacity cannot exceed 50 characters")]
    public string? ListingEngineCapacity { get; set; }

    [StringLength(100, ErrorMessage = "VIN cannot exceed 100 characters")]
    public string? ListingVin { get; set; }

    [StringLength(50, ErrorMessage = "Body type cannot exceed 50 characters")]
    public string? ListingBody { get; set; }
    [Range(1, 50, ErrorMessage = "Seat count must be between 1 and 10")]

    public int? ListingSeat { get; set; }

    [StringLength(50, ErrorMessage = "Wheel type cannot exceed 50 characters")]
    public string? ListingWheel { get; set; }

    [Range(1, 10, ErrorMessage = "Door count must be between 1 and 10")]
    public int? ListingDoor { get; set; }

    [Range(0, int.MaxValue, ErrorMessage = "Mileage must be a positive number")]
    public int? ListingMileage { get; set; }

    [Range(1900, 2100, ErrorMessage = "Year must be between 1900 and 2100")]
    public int? ListingModelYear { get; set; }

    [StringLength(50, ErrorMessage = "Type cannot exceed 50 characters")]
    public string? ListingType { get; set; }
    [Range(1900, 2100, ErrorMessage = "Year must be between 1900 and 2100")]
    public int? ListingYear { get; set; }

    [StringLength(50, ErrorMessage = "Condition cannot exceed 50 characters")]
    public string? ListingCondition { get; set; }

    [StringLength(50, ErrorMessage = "Engine size cannot exceed 50 characters")]
    public string? ListingEngineSize { get; set; }

    // Operating Hours
    [StringLength(100, ErrorMessage = "Monday hours cannot exceed 100 characters")]
    public string? ListingOhMonday { get; set; }

    [StringLength(100, ErrorMessage = "Tuesday hours cannot exceed 100 characters")]
    public string? ListingOhTuesday { get; set; }

    [StringLength(100, ErrorMessage = "Wednesday hours cannot exceed 100 characters")]
    public string? ListingOhWednesday { get; set; }

    [StringLength(100, ErrorMessage = "Thursday hours cannot exceed 100 characters")]
    public string? ListingOhThursday { get; set; }

    [StringLength(100, ErrorMessage = "Friday hours cannot exceed 100 characters")]
    public string? ListingOhFriday { get; set; }

    [StringLength(100, ErrorMessage = "Saturday hours cannot exceed 100 characters")]
    public string? ListingOhSaturday { get; set; }

    [StringLength(100, ErrorMessage = "Sunday hours cannot exceed 100 characters")]
    public string? ListingOhSunday { get; set; }

    [Required(ErrorMessage = "Featured photo is required")]
    public IFormFile? ListingFeaturedPhoto { get; set; }

    [Required(ErrorMessage = "Brand is required")]
    public int ListingBrandId { get; set; }

    [Required(ErrorMessage = "Location is required")]
    public int ListingLocationId { get; set; }

    [StringLength(255, ErrorMessage = "SEO title cannot exceed 255 characters")]
    public string? SeoTitle { get; set; }

    [StringLength(500, ErrorMessage = "SEO meta description cannot exceed 500 characters")]
    public string? SeoMetaDescription { get; set; }

    public bool IsFeatured { get; set; } = false;
    public bool IsActive { get; set; } = false;
    public string ListingStatus { get; set; } = CarPointCMS.Common.ListingStatus.Pending;

    // Collections for dropdowns
    public List<SelectListItem> Brands { get; set; } = new List<SelectListItem>();
    public List<SelectListItem> Locations { get; set; } = new List<SelectListItem>();
    public List<SelectListItem> Amenities { get; set; } = new List<SelectListItem>();

    // Selected amenities
    public List<int> SelectedAmenities { get; set; } = new List<int>();

    // Additional photos
    public List<IFormFile> AdditionalPhotos { get; set; } = new List<IFormFile>();

    // Videos
    public List<string> YoutubeVideoIds { get; set; } = new List<string>();

    // Social items
    public List<string> SocialIcons { get; set; } = new List<string>();
    public List<string> SocialUrls { get; set; } = new List<string>();

    // Additional features
    public List<string> AdditionalFeatureNames { get; set; } = new List<string>();
    public List<string> AdditionalFeatureValues { get; set; } = new List<string>();
}
    public class ListingEditViewModel : ListingCreateViewModel
    {
        public int Id { get; set; }
        public string? ExistingFeaturedPhoto { get; set; }
        public string? ListingStatus { get; set; }

        // Existing related data
        public List<ListingPhoto> ExistingPhotos { get; set; } = new List<ListingPhoto>();
        public List<ListingVideo> ExistingVideos { get; set; } = new List<ListingVideo>();
        public List<ListingSocialItem> ExistingSocialItems { get; set; } = new List<ListingSocialItem>();
        public List<ListingAdditionalFeature> ExistingAdditionalFeatures { get; set; } = new List<ListingAdditionalFeature>();
    }

    public class AdminListingDetailViewModel
    {
        public Listing Listing { get; set; } = null!;
        public List<ListingPhoto> Photos { get; set; } = new List<ListingPhoto>();
        public List<ListingVideo> Videos { get; set; } = new List<ListingVideo>();
        public List<ListingSocialItem> SocialItems { get; set; } = new List<ListingSocialItem>();
        public List<ListingAdditionalFeature> AdditionalFeatures { get; set; } = new List<ListingAdditionalFeature>();
        public List<Amenity> Amenities { get; set; } = new List<Amenity>();
        public int ViewCount { get; set; }
        public int WishlistCount { get; set; }
        public bool IsWishlisted { get; set; }
    }

    public class ListingManagementViewModel
    {
        public IEnumerable<Listing> Listings { get; set; } = new List<Listing>();
        public string? SearchText { get; set; }
        public int? BrandFilter { get; set; }
        public int? LocationFilter { get; set; }
        public string? StatusFilter { get; set; }
        public bool? FeaturedFilter { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public bool HasPreviousPage => Page > 1;
        public bool HasNextPage => Page < TotalPages;

        // For dropdowns
        public SelectList? BrandOptions { get; set; }
        public SelectList? LocationOptions { get; set; }
        public SelectList? StatusOptions { get; set; }
        public SelectList? FeaturedOptions { get; set; }
    }
}
