namespace CarPointCMS.Services.Interfaces
{
    public interface IFileUploadService
    {
        // Image Upload
        Task<string> UploadImageAsync(IFormFile file, string folder);
        Task<List<string>> UploadImagesAsync(List<IFormFile> files, string folder);
        Task<string> UploadListingPhotoAsync(IFormFile file, int listingId);
        Task<List<string>> UploadListingPhotosAsync(List<IFormFile> files, int listingId);
        Task<string> UploadUserPhotoAsync(IFormFile file, int userId);
        Task<string> UploadBrandPhotoAsync(IFormFile file, int brandId);
        Task<string> UploadLocationPhotoAsync(IFormFile file, int locationId);
        
        // File Upload
        Task<string> UploadFileAsync(IFormFile file, string folder);
        Task<List<string>> UploadFilesAsync(List<IFormFile> files, string folder);
        
        // File Management
        Task<bool> DeleteFileAsync(string filePath);
        Task<bool> DeleteFilesAsync(List<string> filePaths);
        Task<bool> FileExistsAsync(string filePath);
        Task<long> GetFileSizeAsync(string filePath);
        
        // Image Processing
        Task<string> ResizeImageAsync(string imagePath, int width, int height);
        Task<string> CreateThumbnailAsync(string imagePath, int size = 150);
        Task<bool> IsValidImageAsync(IFormFile file);
        Task<bool> IsValidFileAsync(IFormFile file, string[] allowedExtensions);
        
        // Validation
        bool IsValidImageExtension(string fileName);
        bool IsValidFileSize(IFormFile file, long maxSizeInBytes);
        string GetUniqueFileName(string originalFileName);
        string GetFileExtension(string fileName);
        
        // Path Management
        string GetUploadPath(string folder);
        void DeleteFile(string fileName, string folder);
        string GetRelativePath(string fullPath);
        string GetAbsolutePath(string relativePath);
    }
}
