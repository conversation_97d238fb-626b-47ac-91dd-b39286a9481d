using Microsoft.AspNetCore.Mvc;
using CarPointCMS.Data;
using CarPointCMS.Models.ViewModels;
using CarPointCMS.Models.Entities;
using CarPointCMS.Controllers;
using CarPointCMS.Attributes;
using CarPointCMS.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Text.RegularExpressions;
using CarPointCMS.Services.Interfaces;

namespace CarPointCMS.Areas.Admin.Controllers
{
    [Area("Admin")]
    [AdminOnly]
    public class ListingController : BaseController
    {
        private readonly ApplicationDbContext _context;
        private readonly IFileUploadService _fileUploadService;

        public ListingController(ApplicationDbContext context, CarPointCMS.Services.IAuthenticationService authService, IFileUploadService fileUploadService)
            : base(authService)
        {
            _context = context;
            _fileUploadService = fileUploadService;
        }

        public async Task<IActionResult> Index()
        {
            var listings = await _context.Listings
                .Include(l => l.ListingBrand)
                .Include(l => l.ListingLocation)
                .Include(l => l.User)
                .Include(l => l.Admin)
                .OrderByDescending(l => l.CreatedAt)
                .ToListAsync();

            return View(listings);
        }

        [HttpGet]
        public async Task<IActionResult> Create()
        {
            var brands = await _context.ListingBrands
                .OrderBy(b => b.ListingBrandName)
                .ToListAsync();

            var locations = await _context.ListingLocations
                .OrderBy(l => l.ListingLocationName)
                .ToListAsync();

            var amenities = await _context.Amenities
                .OrderBy(a => a.AmenityName)
                .ToListAsync();

            var model = new ListingCreateViewModel
            {
                Brands = brands.Select(b => new SelectListItem
                {
                    Value = b.Id.ToString(),
                    Text = b.ListingBrandName
                }).ToList(),
                Locations = locations.Select(l => new SelectListItem
                {
                    Value = l.Id.ToString(),
                    Text = l.ListingLocationName
                }).ToList(),
                Amenities = amenities.Select(a => new SelectListItem
                {
                    Value = a.Id.ToString(),
                    Text = a.AmenityName
                }).ToList()
            };

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> Create(ListingCreateViewModel model)
        {
            // Reload dropdowns for validation errors
            await LoadDropdownData(model);

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {

                // Check if listing name already exists
                var existingListing = await _context.Listings
                    .FirstOrDefaultAsync(l => l.ListingName == model.ListingName);

                if (existingListing != null)
                {
                    ModelState.AddModelError("ListingName", "Listing name already exists.");
                    return View(model);
                }

                // Generate slug if not provided
                var slug = !string.IsNullOrEmpty(model.ListingSlug)
                    ? model.ListingSlug
                    : GenerateSlug(model.ListingName);

                // Check if slug already exists
                var existingSlug = await _context.Listings
                    .FirstOrDefaultAsync(l => l.ListingSlug == slug);

                if (existingSlug != null)
                {
                    ModelState.AddModelError("ListingSlug", "Listing slug already exists.");
                    return View(model);
                }

                // Handle featured photo upload
                string? featuredPhotoFileName = null;
                if (model.ListingFeaturedPhoto != null && model.ListingFeaturedPhoto.Length > 0)
                {
                    featuredPhotoFileName = await _fileUploadService.UploadImageAsync(model.ListingFeaturedPhoto, "listing_featured_photos");
                }

                var listing = new Listing
                {
                    ListingName = model.ListingName,
                    ListingSlug = slug,
                    ListingDescription = model.ListingDescription,
                    ListingAddress = model.ListingAddress,
                    ListingPhone = model.ListingPhone,
                    ListingEmail = model.ListingEmail,
                    ListingWebsite = model.ListingWebsite,
                    ListingMap = model.ListingMap,
                    ListingPrice = model.ListingPrice,
                    ListingExteriorColor = model.ListingExteriorColor,
                    ListingInteriorColor = model.ListingInteriorColor,
                    ListingYear = model.ListingYear,
                    ListingMileage = model.ListingMileage,
                    ListingFuelType = model.ListingFuelType,
                    ListingTransmission = model.ListingTransmission,
                    ListingCondition = model.ListingCondition,
                    ListingEngineSize = model.ListingEngineSize,
                    ListingCylinder = model.ListingCylinder,
                    ListingWheel = model.ListingWheel,
                    ListingBody = model.ListingBody,
                    ListingSeat = model.ListingSeat,
                    ListingModelYear = model.ListingModelYear,
                    ListingEngineCapacity = model.ListingEngineCapacity,
                    ListingDoor = model.ListingDoor,
                    ListingVin = model.ListingVin,
                    ListingType = model.ListingType,
                    ListingOhMonday = model.ListingOhMonday,
                    ListingOhTuesday = model.ListingOhTuesday,
                    ListingOhWednesday = model.ListingOhWednesday,
                    ListingOhThursday = model.ListingOhThursday,
                    ListingOhFriday = model.ListingOhFriday,
                    ListingOhSaturday = model.ListingOhSaturday,
                    ListingOhSunday = model.ListingOhSunday,
                    ListingFeaturedPhoto = featuredPhotoFileName,
                    ListingBrandId = model.ListingBrandId,
                    ListingLocationId = model.ListingLocationId,
                    UserId = null, // Admin created listing
                    AdminId = GetCurrentAdminId(),
                    UserType = "Admin",
                    SeoTitle = model.SeoTitle,
                    SeoMetaDescription = model.SeoMetaDescription,
                    ListingStatus = ListingStatus.Active,
                    IsFeatured = model.IsFeatured,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Listings.Add(listing);
                await _context.SaveChangesAsync();

                // Save amenities
                if (model.SelectedAmenities != null && model.SelectedAmenities.Any())
                {
                    foreach (var amenityId in model.SelectedAmenities)
                    {
                        var listingAmenity = new ListingAmenity
                        {
                            ListingId = listing.Id,
                            AmenityId = amenityId
                        };
                        _context.ListingAmenities.Add(listingAmenity);
                    }
                }

                // Save additional photos
                if (model.AdditionalPhotos != null && model.AdditionalPhotos.Any())
                {
                    foreach (var photo in model.AdditionalPhotos)
                    {
                        if (photo.Length > 0)
                        {
                            var photoFileName = await _fileUploadService.UploadImageAsync(photo, "listing_photos");
                            var listingPhoto = new ListingPhoto
                            {
                                ListingId = listing.Id,
                                Photo = photoFileName
                            };
                            _context.ListingPhotos.Add(listingPhoto);
                        }
                    }
                }

                // Save videos
                if (model.YoutubeVideoIds != null && model.YoutubeVideoIds.Any())
                {
                    foreach (var videoId in model.YoutubeVideoIds.Where(v => !string.IsNullOrEmpty(v)))
                    {
                        var listingVideo = new ListingVideo
                        {
                            ListingId = listing.Id,
                            YoutubeVideoId = videoId
                        };
                        _context.ListingVideos.Add(listingVideo);
                    }
                }

                // Save social items
                if (model.SocialIcons != null && model.SocialUrls != null)
                {
                    for (int i = 0; i < Math.Min(model.SocialIcons.Count, model.SocialUrls.Count); i++)
                    {
                        if (!string.IsNullOrEmpty(model.SocialIcons[i]) && !string.IsNullOrEmpty(model.SocialUrls[i]))
                        {
                            var socialItem = new ListingSocialItem
                            {
                                ListingId = listing.Id,
                                SocialIcon = model.SocialIcons[i],
                                SocialUrl = model.SocialUrls[i]
                            };
                            _context.ListingSocialItems.Add(socialItem);
                        }
                    }
                }

                // Save additional features
                if (model.AdditionalFeatureNames != null && model.AdditionalFeatureValues != null)
                {
                    for (int i = 0; i < Math.Min(model.AdditionalFeatureNames.Count, model.AdditionalFeatureValues.Count); i++)
                    {
                        if (!string.IsNullOrEmpty(model.AdditionalFeatureNames[i]) && !string.IsNullOrEmpty(model.AdditionalFeatureValues[i]))
                        {
                            var additionalFeature = new ListingAdditionalFeature
                            {
                                ListingId = listing.Id,
                                AdditionalFeatureName = model.AdditionalFeatureNames[i],
                                AdditionalFeatureValue = model.AdditionalFeatureValues[i]
                            };
                            _context.ListingAdditionalFeatures.Add(additionalFeature);
                        }
                    }
                }

                await _context.SaveChangesAsync();

                SetSuccessMessage("Listing has been created successfully.");
                return RedirectToActionInArea("Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while creating the listing.");
                return View(model);
            }
        }

        private async Task LoadDropdownData(ListingCreateViewModel model)
        {
            var brands = await _context.ListingBrands
                .OrderBy(b => b.ListingBrandName)
                .ToListAsync();

            var locations = await _context.ListingLocations
                .OrderBy(l => l.ListingLocationName)
                .ToListAsync();

            var amenities = await _context.Amenities
                .OrderBy(a => a.AmenityName)
                .ToListAsync();

            model.Brands = brands.Select(b => new SelectListItem
            {
                Value = b.Id.ToString(),
                Text = b.ListingBrandName
            }).ToList();

            model.Locations = locations.Select(l => new SelectListItem
            {
                Value = l.Id.ToString(),
                Text = l.ListingLocationName
            }).ToList();

            model.Amenities = amenities.Select(a => new SelectListItem
            {
                Value = a.Id.ToString(),
                Text = a.AmenityName
            }).ToList();
        }

        private async Task LoadDropdownDataForEdit(ListingEditViewModel model)
        {
            var brands = await _context.ListingBrands
                .OrderBy(b => b.ListingBrandName)
                .ToListAsync();

            var locations = await _context.ListingLocations
                .OrderBy(l => l.ListingLocationName)
                .ToListAsync();

            var amenities = await _context.Amenities
                .OrderBy(a => a.AmenityName)
                .ToListAsync();

            model.Brands = brands.Select(b => new SelectListItem
            {
                Value = b.Id.ToString(),
                Text = b.ListingBrandName,
                Selected = b.Id == model.ListingBrandId
            }).ToList();

            model.Locations = locations.Select(l => new SelectListItem
            {
                Value = l.Id.ToString(),
                Text = l.ListingLocationName,
                Selected = l.Id == model.ListingLocationId
            }).ToList();

            model.Amenities = amenities.Select(a => new SelectListItem
            {
                Value = a.Id.ToString(),
                Text = a.AmenityName,
                Selected = model.SelectedAmenities?.Contains(a.Id) == true
            }).ToList();
        }

        private string GenerateSlug(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            string slug = input.ToLower();
            slug = Regex.Replace(slug, @"[^a-z0-9\s-]", "");
            slug = Regex.Replace(slug, @"\s+", "-");
            slug = Regex.Replace(slug, @"-+", "-");
            slug = slug.Trim('-');

            return slug;
        }

        [HttpGet]
        public async Task<IActionResult> Edit(int id)
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            var listing = await _context.Listings
                .Include(l => l.ListingBrand)
                .Include(l => l.ListingLocation)
                .Include(l => l.ListingAmenities)
                .Include(l => l.ListingPhotos)
                .Include(l => l.ListingVideos)
                .Include(l => l.ListingSocialItems)
                .Include(l => l.ListingAdditionalFeatures)
                .FirstOrDefaultAsync(l => l.Id == id);

            if (listing == null)
            {
                TempData["error"] = "Listing not found.";
                return RedirectToActionInArea("Index");
            }

            var brands = await _context.ListingBrands
                .OrderBy(b => b.ListingBrandName)
                .ToListAsync();

            var locations = await _context.ListingLocations
                .OrderBy(l => l.ListingLocationName)
                .ToListAsync();

            var amenities = await _context.Amenities
                .OrderBy(a => a.AmenityName)
                .ToListAsync();

            var model = new ListingEditViewModel
            {
                Id = listing.Id,
                ListingName = listing.ListingName,
                ListingSlug = listing.ListingSlug,
                ListingDescription = listing.ListingDescription,
                ListingAddress = listing.ListingAddress,
                ListingPhone = listing.ListingPhone,
                ListingEmail = listing.ListingEmail,
                ListingWebsite = listing.ListingWebsite,
                ListingMap = listing.ListingMap,
                ListingPrice = listing.ListingPrice,
                ListingExteriorColor = listing.ListingExteriorColor,
                ListingInteriorColor = listing.ListingInteriorColor,
                ListingYear = listing.ListingYear,
                ListingMileage = listing.ListingMileage,
                ListingFuelType = listing.ListingFuelType,
                ListingTransmission = listing.ListingTransmission,
                ListingCondition = listing.ListingCondition,
                ListingEngineSize = listing.ListingEngineSize,
                ListingCylinder = listing.ListingCylinder,
                ListingDoor = listing.ListingDoor,
                ListingVin = listing.ListingVin,
                ListingType = listing.ListingType,
                IsActive = listing.IsActive,
                ListingEngineCapacity = listing.ListingEngineCapacity,
                ListingBody = listing.ListingBody,
                ListingSeat = listing.ListingSeat,
                ListingWheel = listing.ListingWheel,
                ListingModelYear = listing.ListingModelYear,
                ListingOhMonday = listing.ListingOhMonday,
                ListingOhTuesday = listing.ListingOhTuesday,
                ListingOhWednesday = listing.ListingOhWednesday,
                ListingOhThursday = listing.ListingOhThursday,
                ListingOhFriday = listing.ListingOhFriday,
                ListingOhSaturday = listing.ListingOhSaturday,
                ListingOhSunday = listing.ListingOhSunday,
                ExistingFeaturedPhoto = listing.ListingFeaturedPhoto,
                ListingBrandId = listing.ListingBrandId,
                ListingLocationId = listing.ListingLocationId,
                SeoTitle = listing.SeoTitle,
                SeoMetaDescription = listing.SeoMetaDescription,
                ListingStatus = listing.ListingStatus,
                IsFeatured = listing.IsFeatured,
                SelectedAmenities = listing.ListingAmenities.Select(la => la.AmenityId).ToList(),
                ExistingPhotos = listing.ListingPhotos.ToList(),
                ExistingVideos = listing.ListingVideos.ToList(),
                ExistingSocialItems = listing.ListingSocialItems.ToList(),
                ExistingAdditionalFeatures = listing.ListingAdditionalFeatures.ToList(),
                Brands = brands.Select(b => new SelectListItem
                {
                    Value = b.Id.ToString(),
                    Text = b.ListingBrandName,
                    Selected = b.Id == listing.ListingBrandId
                }).ToList(),
                Locations = locations.Select(l => new SelectListItem
                {
                    Value = l.Id.ToString(),
                    Text = l.ListingLocationName,
                    Selected = l.Id == listing.ListingLocationId
                }).ToList(),
                Amenities = amenities.Select(a => new SelectListItem
                {
                    Value = a.Id.ToString(),
                    Text = a.AmenityName,
                    Selected = listing.ListingAmenities.Any(la => la.AmenityId == a.Id)
                }).ToList()
            };

            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> Edit(ListingEditViewModel model)
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            // Reload dropdowns for validation errors
            await LoadDropdownDataForEdit(model);

            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var listing = await _context.Listings
                    .Include(l => l.ListingAmenities)
                    .Include(l => l.ListingPhotos)
                    .Include(l => l.ListingVideos)
                    .Include(l => l.ListingSocialItems)
                    .Include(l => l.ListingAdditionalFeatures)
                    .FirstOrDefaultAsync(l => l.Id == model.Id);

                if (listing == null)
                {
                    SetErrorMessage("Listing not found.");
                    return RedirectToActionInArea("Index");
                }

                // Check if listing name already exists (excluding current listing)
                var existingListing = await _context.Listings
                    .FirstOrDefaultAsync(l => l.ListingName == model.ListingName && l.Id != model.Id);

                if (existingListing != null)
                {
                    ModelState.AddModelError("ListingName", "Listing name already exists.");
                    return View(model);
                }

                // Generate slug if changed
                var slug = !string.IsNullOrEmpty(model.ListingSlug)
                    ? model.ListingSlug
                    : GenerateSlug(model.ListingName);

                // Check if slug already exists (excluding current listing)
                var existingSlug = await _context.Listings
                    .FirstOrDefaultAsync(l => l.ListingSlug == slug && l.Id != model.Id);

                if (existingSlug != null)
                {
                    ModelState.AddModelError("ListingSlug", "Listing slug already exists.");
                    return View(model);
                }

                // Handle featured photo upload
                if (model.ListingFeaturedPhoto != null && model.ListingFeaturedPhoto.Length > 0)
                {
                    // Delete old featured photo
                    if (!string.IsNullOrEmpty(listing.ListingFeaturedPhoto))
                    {
                        _fileUploadService.DeleteFile(listing.ListingFeaturedPhoto, "listing_featured_photos");
                    }

                    listing.ListingFeaturedPhoto = await _fileUploadService.UploadImageAsync(model.ListingFeaturedPhoto, "listing_featured_photos");
                }

                // Update listing properties
                listing.ListingName = model.ListingName;
                listing.ListingSlug = slug;
                listing.ListingDescription = model.ListingDescription;
                listing.ListingAddress = model.ListingAddress;
                listing.ListingPhone = model.ListingPhone;
                listing.ListingEmail = model.ListingEmail;
                listing.ListingWebsite = model.ListingWebsite;
                listing.ListingMap = model.ListingMap;
                listing.ListingPrice = model.ListingPrice;
                listing.ListingExteriorColor = model.ListingExteriorColor;
                listing.ListingInteriorColor = model.ListingInteriorColor;
                listing.ListingYear = model.ListingYear;
                listing.ListingMileage = model.ListingMileage;
                listing.ListingFuelType = model.ListingFuelType;
                listing.ListingTransmission = model.ListingTransmission;
                listing.ListingCondition = model.ListingCondition;
                listing.ListingEngineSize = model.ListingEngineSize;
                listing.ListingCylinder = model.ListingCylinder;
                listing.ListingWheel = model.ListingWheel;
                listing.ListingBody = model.ListingBody;
                listing.ListingSeat = model.ListingSeat;
                listing.ListingModelYear = model.ListingModelYear;
                listing.ListingEngineCapacity = model.ListingEngineCapacity;
                listing.ListingDoor = model.ListingDoor;
                listing.ListingVin = model.ListingVin;
                listing.ListingType = model.ListingType;
                listing.ListingOhMonday = model.ListingOhMonday;
                listing.ListingOhTuesday = model.ListingOhTuesday;
                listing.ListingOhWednesday = model.ListingOhWednesday;
                listing.ListingOhThursday = model.ListingOhThursday;
                listing.ListingOhFriday = model.ListingOhFriday;
                listing.ListingOhSaturday = model.ListingOhSaturday;
                listing.ListingOhSunday = model.ListingOhSunday;
                listing.ListingBrandId = model.ListingBrandId;
                listing.ListingLocationId = model.ListingLocationId;
                listing.SeoTitle = model.SeoTitle;
                listing.SeoMetaDescription = model.SeoMetaDescription;
                listing.ListingStatus = model.ListingStatus ?? ListingStatus.Pending;
                listing.IsFeatured = model.IsFeatured;
                listing.UpdatedAt = DateTime.UtcNow;

                // Update amenities
                _context.ListingAmenities.RemoveRange(listing.ListingAmenities);
                if (model.SelectedAmenities != null && model.SelectedAmenities.Any())
                {
                    foreach (var amenityId in model.SelectedAmenities)
                    {
                        var listingAmenity = new ListingAmenity
                        {
                            ListingId = listing.Id,
                            AmenityId = amenityId
                        };
                        _context.ListingAmenities.Add(listingAmenity);
                    }
                }

                // Add new photos
                if (model.AdditionalPhotos != null && model.AdditionalPhotos.Any())
                {
                    foreach (var photo in model.AdditionalPhotos)
                    {
                        if (photo.Length > 0)
                        {
                            var photoFileName = await _fileUploadService.UploadImageAsync(photo, "listing_photos");
                            var listingPhoto = new ListingPhoto
                            {
                                ListingId = listing.Id,
                                Photo = photoFileName
                            };
                            _context.ListingPhotos.Add(listingPhoto);
                        }
                    }
                }

                // Add new videos
                if (model.YoutubeVideoIds != null && model.YoutubeVideoIds.Any())
                {
                    foreach (var videoId in model.YoutubeVideoIds.Where(v => !string.IsNullOrEmpty(v)))
                    {
                        var listingVideo = new ListingVideo
                        {
                            ListingId = listing.Id,
                            YoutubeVideoId = videoId
                        };
                        _context.ListingVideos.Add(listingVideo);
                    }
                }

                // Add new social items
                if (model.SocialIcons != null && model.SocialUrls != null)
                {
                    for (int i = 0; i < Math.Min(model.SocialIcons.Count, model.SocialUrls.Count); i++)
                    {
                        if (!string.IsNullOrEmpty(model.SocialIcons[i]) && !string.IsNullOrEmpty(model.SocialUrls[i]))
                        {
                            var socialItem = new ListingSocialItem
                            {
                                ListingId = listing.Id,
                                SocialIcon = model.SocialIcons[i],
                                SocialUrl = model.SocialUrls[i]
                            };
                            _context.ListingSocialItems.Add(socialItem);
                        }
                    }
                }

                // Add new additional features
                if (model.AdditionalFeatureNames != null && model.AdditionalFeatureValues != null)
                {
                    for (int i = 0; i < Math.Min(model.AdditionalFeatureNames.Count, model.AdditionalFeatureValues.Count); i++)
                    {
                        if (!string.IsNullOrEmpty(model.AdditionalFeatureNames[i]) && !string.IsNullOrEmpty(model.AdditionalFeatureValues[i]))
                        {
                            var additionalFeature = new ListingAdditionalFeature
                            {
                                ListingId = listing.Id,
                                AdditionalFeatureName = model.AdditionalFeatureNames[i],
                                AdditionalFeatureValue = model.AdditionalFeatureValues[i]
                            };
                            _context.ListingAdditionalFeatures.Add(additionalFeature);
                        }
                    }
                }

                await _context.SaveChangesAsync();

                SetSuccessMessage("Listing has been updated successfully.");
                return RedirectToActionInArea("Index");
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("", "An error occurred while updating the listing.");
                return View(model);
            }
        }

        [HttpPost]
        public async Task<IActionResult> ChangeStatus(int id)
        {
            if (!IsAdminLoggedIn())
                return Json(new { success = false, message = "Unauthorized" });

            try
            {
                var listing = await _context.Listings.FindAsync(id);
                if (listing == null)
                {
                    return Json(new { success = false, message = "Listing not found" });
                }

                listing.ListingStatus = listing.ListingStatus == ListingStatus.Active ? ListingStatus.Pending : ListingStatus.Active;
                listing.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "Status updated successfully", newStatus = listing.ListingStatus });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "An error occurred while updating status" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Delete(int id)
        {
            if (!IsAdminLoggedIn())
                return RedirectToLogin();

            try
            {
                var listing = await _context.Listings
                    .Include(l => l.ListingPhotos)
                    .Include(l => l.ListingVideos)
                    .Include(l => l.ListingAmenities)
                    .Include(l => l.ListingSocialItems)
                    .Include(l => l.ListingAdditionalFeatures)
                    .FirstOrDefaultAsync(l => l.Id == id);

                if (listing == null)
                {
                    TempData["error"] = "Listing not found.";
                    return RedirectToActionInArea("Index");
                }

                // Delete featured photo
                if (!string.IsNullOrEmpty(listing.ListingFeaturedPhoto))
                {
                    _fileUploadService.DeleteFile(listing.ListingFeaturedPhoto, "listing_featured_photos");
                }

                // Delete additional photos
                foreach (var photo in listing.ListingPhotos)
                {
                    _fileUploadService.DeleteFile(photo.Photo, "listing_photos");
                }

                // Remove all related data
                _context.ListingPhotos.RemoveRange(listing.ListingPhotos);
                _context.ListingVideos.RemoveRange(listing.ListingVideos);
                _context.ListingAmenities.RemoveRange(listing.ListingAmenities);
                _context.ListingSocialItems.RemoveRange(listing.ListingSocialItems);
                _context.ListingAdditionalFeatures.RemoveRange(listing.ListingAdditionalFeatures);

                // Remove the listing itself
                _context.Listings.Remove(listing);

                await _context.SaveChangesAsync();

                TempData["success"] = "Listing has been deleted successfully.";
                return RedirectToActionInArea("Index");
            }
            catch (Exception ex)
            {
                TempData["error"] = "An error occurred while deleting the listing.";
                return RedirectToActionInArea("Index");
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeletePhoto(int id)
        {
            if (!IsAdminLoggedIn())
                return Json(new { success = false, message = "Unauthorized" });

            try
            {
                var photo = await _context.ListingPhotos.FindAsync(id);
                if (photo == null)
                {
                    return Json(new { success = false, message = "Photo not found" });
                }

                _fileUploadService.DeleteFile(photo.Photo, "listing_photos");

                _context.ListingPhotos.Remove(photo);
                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "Photo deleted successfully" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "An error occurred while deleting photo" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteVideo(int id)
        {
            if (!IsAdminLoggedIn())
                return Json(new { success = false, message = "Unauthorized" });

            try
            {
                var video = await _context.ListingVideos.FindAsync(id);
                if (video == null)
                {
                    return Json(new { success = false, message = "Video not found" });
                }

                _context.ListingVideos.Remove(video);
                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "Video deleted successfully" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "An error occurred while deleting video" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteSocialItem(int id)
        {
            if (!IsAdminLoggedIn())
                return Json(new { success = false, message = "Unauthorized" });

            try
            {
                var socialItem = await _context.ListingSocialItems.FindAsync(id);
                if (socialItem == null)
                {
                    return Json(new { success = false, message = "Social item not found" });
                }

                _context.ListingSocialItems.Remove(socialItem);
                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "Social item deleted successfully" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "An error occurred while deleting social item" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteAdditionalFeature(int id)
        {
            if (!IsAdminLoggedIn())
                return Json(new { success = false, message = "Unauthorized" });

            try
            {
                var feature = await _context.ListingAdditionalFeatures.FindAsync(id);
                if (feature == null)
                {
                    return Json(new { success = false, message = "Additional feature not found" });
                }

                _context.ListingAdditionalFeatures.Remove(feature);
                await _context.SaveChangesAsync();

                return Json(new { success = true, message = "Additional feature deleted successfully" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "An error occurred while deleting additional feature" });
            }
        }
    }
}
