@{
    ViewData["Title"] = "Database Seeder";
}

<div class="main-content">
    <div class="section__content section__content--p30">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="overview-wrap">
                        <h2 class="title-1">Database Seeder</h2>
                    </div>
                </div>
            </div>

            <div class="row m-t-25">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <strong>Database Seeding Options</strong>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <strong>Note:</strong> This tool will populate your database with sample data from the original CarPoint SQL file. 
                                Use this for development and testing purposes only.
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            <h5>Seed All Data</h5>
                                        </div>
                                        <div class="card-body">
                                            <p>Seeds all data including admins, users, categories, amenities, brands, locations, blogs, FAQs, email templates, settings, packages, listings, and package purchases.</p>
                                            <button type="button" class="btn btn-primary btn-block" onclick="seedData('all')">
                                                <i class="fas fa-database"></i> Seed All Data
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-danger text-white">
                                            <h5>Clear All Data</h5>
                                        </div>
                                        <div class="card-body">
                                            <p>Removes all data from the database. This action cannot be undone!</p>
                                            <button type="button" class="btn btn-danger btn-block" onclick="clearData()">
                                                <i class="fas fa-trash"></i> Clear All Data
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <h5>Selective Seeding</h5>
                                    <p>Seed specific types of data individually:</p>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h6>Core Data</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="small">Admins, Users, Categories, Amenities, Brands, Locations</p>
                                            <button type="button" class="btn btn-success btn-sm btn-block" onclick="seedData('core')">
                                                <i class="fas fa-cog"></i> Seed Core
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <h6>Content Data</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="small">Blogs, FAQs, Email Templates, General Settings</p>
                                            <button type="button" class="btn btn-info btn-sm btn-block" onclick="seedData('content')">
                                                <i class="fas fa-file-alt"></i> Seed Content
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-header bg-warning text-white">
                                            <h6>Listing Data</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="small">Listings, Photos, Videos, Social Items, Features</p>
                                            <button type="button" class="btn btn-warning btn-sm btn-block" onclick="seedData('listings')">
                                                <i class="fas fa-car"></i> Seed Listings
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-header bg-secondary text-white">
                                            <h6>Package Data</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="small">Packages, Package Purchases</p>
                                            <button type="button" class="btn btn-secondary btn-sm btn-block" onclick="seedData('packages')">
                                                <i class="fas fa-box"></i> Seed Packages
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="alert alert-warning">
                                        <strong>Important:</strong>
                                        <ul class="mb-0">
                                            <li>Make sure you have a backup of your database before running any seeding operations</li>
                                            <li>Seeding will only add data if the tables are empty (no duplicates will be created)</li>
                                            <li>Some operations may take a few seconds to complete</li>
                                            <li>The seeded data includes sample images and content from the original CarPoint project</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function seedData(type) {
            let url = '';
            let message = '';
            
            switch(type) {
                case 'all':
                    url = '@Url.Action("SeedAll", "DatabaseSeeder", new { area = "Admin" })';
                    message = 'This will seed all data. Continue?';
                    break;
                case 'core':
                    url = '@Url.Action("SeedCore", "DatabaseSeeder", new { area = "Admin" })';
                    message = 'This will seed core data (admins, users, categories, etc.). Continue?';
                    break;
                case 'content':
                    url = '@Url.Action("SeedContent", "DatabaseSeeder", new { area = "Admin" })';
                    message = 'This will seed content data (blogs, FAQs, etc.). Continue?';
                    break;
                case 'listings':
                    url = '@Url.Action("SeedListings", "DatabaseSeeder", new { area = "Admin" })';
                    message = 'This will seed listing data. Continue?';
                    break;
                case 'packages':
                    url = '@Url.Action("SeedPackages", "DatabaseSeeder", new { area = "Admin" })';
                    message = 'This will seed package data. Continue?';
                    break;
            }

            if (confirm(message)) {
                performSeeding(url);
            }
        }

        function clearData() {
            if (confirm('This will delete ALL data from the database. This action cannot be undone! Are you sure?')) {
                if (confirm('Are you ABSOLUTELY sure? This will remove all data permanently!')) {
                    performSeeding('@Url.Action("ClearAll", "DatabaseSeeder", new { area = "Admin" })');
                }
            }
        }

        function performSeeding(url) {
            // Show loading state
            $('button').prop('disabled', true);
            toastr.info('Processing... Please wait.');

            $.ajax({
                type: 'POST',
                url: url,
                headers: {
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(xhr, status, error) {
                    toastr.error('An error occurred: ' + error);
                },
                complete: function() {
                    // Re-enable buttons
                    $('button').prop('disabled', false);
                }
            });
        }
    </script>
}

@Html.AntiForgeryToken()
