using CarPointCMS.Data;
using CarPointCMS.Data.Seeders;
using CarPointCMS.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace CarPointCMS.Services
{
    public class DatabaseSeederService : IDatabaseSeederService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DatabaseSeederService> _logger;

        public DatabaseSeederService(ApplicationDbContext context, ILogger<DatabaseSeederService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task SeedDatabaseAsync()
        {
            try
            {
                _logger.LogInformation("Starting database seeding...");

                // Ensure database is created
                await _context.Database.EnsureCreatedAsync();

                // Run all seeders
                var seeder = new DatabaseSeeder(_context);
                await seeder.SeedAllAsync();

                _logger.LogInformation("Database seeding completed successfully!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during database seeding");
                throw;
            }
        }

        public async Task SeedCoreDataAsync()
        {
            try
            {
                _logger.LogInformation("Seeding core data...");

                var seeder = new DatabaseSeeder(_context);
                
                // Seed only core entities
                await seeder.SeedAdminsAsync();
                await seeder.SeedUsersAsync();
                await seeder.SeedCategoriesAsync();
                await seeder.SeedAmenitiesAsync();
                await seeder.SeedListingBrandsAsync();
                await seeder.SeedListingLocationsAsync();

                await _context.SaveChangesAsync();
                _logger.LogInformation("Core data seeding completed!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during core data seeding");
                throw;
            }
        }

        public async Task SeedContentDataAsync()
        {
            try
            {
                _logger.LogInformation("Seeding content data...");

                var seeder = new DatabaseSeeder(_context);
                
                // Seed content entities
                await seeder.SeedBlogsAsync();
                await seeder.SeedFaqsAsync();
                await seeder.SeedEmailTemplatesAsync();
                await seeder.SeedGeneralSettingsAsync();

                await _context.SaveChangesAsync();
                _logger.LogInformation("Content data seeding completed!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during content data seeding");
                throw;
            }
        }

        public async Task SeedListingDataAsync()
        {
            try
            {
                _logger.LogInformation("Seeding listing data...");

                var seeder = new DatabaseSeeder(_context);
                
                // Seed listing entities
                await seeder.SeedListingsAsync();
                await seeder.SeedListingRelatedDataAsync();

                await _context.SaveChangesAsync();
                _logger.LogInformation("Listing data seeding completed!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during listing data seeding");
                throw;
            }
        }

        public async Task SeedPackageDataAsync()
        {
            try
            {
                _logger.LogInformation("Seeding package data...");

                var seeder = new DatabaseSeeder(_context);
                
                // Seed package entities
                await seeder.SeedPackagesAsync();
                await seeder.SeedPackagePurchasesAsync();

                await _context.SaveChangesAsync();
                _logger.LogInformation("Package data seeding completed!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during package data seeding");
                throw;
            }
        }

        public async Task ClearAllDataAsync()
        {
            try
            {
                _logger.LogInformation("Clearing all data from database...");

                // Clear in reverse order of dependencies
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM PackagePurchases");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM Packages");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM ListingAdditionalFeatures");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM ListingSocialItems");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM ListingVideos");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM ListingPhotos");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM ListingAmenities");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM Listings");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM GeneralSettings");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM EmailTemplates");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM Faqs");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM Blogs");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM ListingLocations");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM ListingBrands");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM Amenities");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM Categories");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM Users");
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM Admins");

                // Reset identity columns if using SQL Server
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('PackagePurchases', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Packages', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingAdditionalFeatures', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingSocialItems', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingVideos', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingPhotos', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingAmenities', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Listings', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('GeneralSettings', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('EmailTemplates', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Faqs', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Blogs', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingLocations', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingBrands', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Amenities', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Categories', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Users', RESEED, 0)");
                await _context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Admins', RESEED, 0)");

                _logger.LogInformation("All data cleared from database!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during data clearing");
                throw;
            }
        }
    }
}
