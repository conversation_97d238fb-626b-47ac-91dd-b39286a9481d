using CarPointCMS.Data;
using CarPointCMS.Data.Seeders;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace CarPointCMS
{
    /// <summary>
    /// Standalone database seeding utility
    /// Usage: dotnet run --project CarPointCMS SeedDatabase [options]
    /// Options: --clear, --core, --content, --listings, --packages, --all (default)
    /// </summary>
    public class SeedDatabase
    {
        public static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("CarPoint CMS Database Seeder");
                Console.WriteLine("============================");

                // Build configuration
                var configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false)
                    .AddJsonFile("appsettings.Development.json", optional: true)
                    .Build();

                // Get connection string
                var connectionString = configuration.GetConnectionString("DefaultConnection");
                if (string.IsNullOrEmpty(connectionString))
                {
                    Console.WriteLine("Error: Connection string not found in appsettings.json");
                    return;
                }

                // Setup DbContext
                var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
                optionsBuilder.UseSqlServer(connectionString);

                using var context = new ApplicationDbContext(optionsBuilder.Options);

                // Ensure database exists
                Console.WriteLine("Ensuring database exists...");
                await context.Database.EnsureCreatedAsync();

                // Parse command line arguments
                var option = args.Length > 0 ? args[0].ToLower() : "--all";

                var seeder = new DatabaseSeeder(context);

                switch (option)
                {
                    case "--clear":
                        Console.WriteLine("Clearing all data from database...");
                        await ClearAllData(context);
                        Console.WriteLine("All data cleared successfully!");
                        break;

                    case "--core":
                        Console.WriteLine("Seeding core data...");
                        await seeder.SeedAdminsAsync();
                        await seeder.SeedUsersAsync();
                        await seeder.SeedCategoriesAsync();
                        await seeder.SeedAmenitiesAsync();
                        await seeder.SeedListingBrandsAsync();
                        await seeder.SeedListingLocationsAsync();
                        await context.SaveChangesAsync();
                        Console.WriteLine("Core data seeded successfully!");
                        break;

                    case "--content":
                        Console.WriteLine("Seeding content data...");
                        await seeder.SeedBlogsAsync();
                        await seeder.SeedFaqsAsync();
                        await seeder.SeedEmailTemplatesAsync();
                        await seeder.SeedGeneralSettingsAsync();
                        await context.SaveChangesAsync();
                        Console.WriteLine("Content data seeded successfully!");
                        break;

                    case "--listings":
                        Console.WriteLine("Seeding listing data...");
                        await seeder.SeedListingsAsync();
                        await seeder.SeedListingRelatedDataAsync();
                        await context.SaveChangesAsync();
                        Console.WriteLine("Listing data seeded successfully!");
                        break;

                    case "--packages":
                        Console.WriteLine("Seeding package data...");
                        await seeder.SeedPackagesAsync();
                        await seeder.SeedPackagePurchasesAsync();
                        await context.SaveChangesAsync();
                        Console.WriteLine("Package data seeded successfully!");
                        break;

                    case "--all":
                    default:
                        Console.WriteLine("Seeding all data...");
                        await seeder.SeedAllAsync();
                        Console.WriteLine("All data seeded successfully!");
                        break;
                }

                Console.WriteLine("Database seeding completed!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                Environment.Exit(1);
            }
        }

        private static async Task ClearAllData(ApplicationDbContext context)
        {
            // Clear in reverse order of dependencies
            await context.Database.ExecuteSqlRawAsync("DELETE FROM PackagePurchases");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM Packages");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM ListingAdditionalFeatures");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM ListingSocialItems");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM ListingVideos");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM ListingPhotos");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM ListingAmenities");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM Listings");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM GeneralSettings");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM EmailTemplates");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM Faqs");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM Blogs");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM ListingLocations");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM ListingBrands");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM Amenities");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM Categories");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM Users");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM Admins");

            // Reset identity columns if using SQL Server
            try
            {
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('PackagePurchases', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Packages', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingAdditionalFeatures', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingSocialItems', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingVideos', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingPhotos', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingAmenities', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Listings', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('GeneralSettings', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('EmailTemplates', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Faqs', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Blogs', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingLocations', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('ListingBrands', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Amenities', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Categories', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Users', RESEED, 0)");
                await context.Database.ExecuteSqlRawAsync("DBCC CHECKIDENT ('Admins', RESEED, 0)");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Warning: Could not reset identity columns: {ex.Message}");
            }
        }
    }
}
