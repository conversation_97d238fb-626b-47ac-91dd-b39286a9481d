using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using CarPointCMS.Services.Interfaces;

namespace CarPointCMS.Services.Implementations
{
    public class FileUploadService : IFileUploadService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<FileUploadService> _logger;
        private readonly string[] _allowedImageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };
        private readonly string[] _allowedFileExtensions = { ".pdf", ".doc", ".docx", ".txt", ".zip", ".rar" };
        private readonly long _maxFileSize = 10 * 1024 * 1024; // 10MB
        private readonly long _maxImageSize = 5 * 1024 * 1024; // 5MB

        public FileUploadService(IWebHostEnvironment environment, ILogger<FileUploadService> logger)
        {
            _environment = environment;
            _logger = logger;
        }

        public async Task<string> UploadImageAsync(IFormFile file, string folder)
        {
            if (!await IsValidImageAsync(file))
                throw new ArgumentException("Invalid image file");

            var uploadPath = GetUploadPath(folder);
            Directory.CreateDirectory(uploadPath);

            var fileName = GetUniqueFileName(file.FileName);
            var filePath = Path.Combine(uploadPath, fileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            _logger.LogInformation($"Image uploaded successfully: {fileName}");
            return GetRelativePath(filePath);
        }

        public async Task<List<string>> UploadImagesAsync(List<IFormFile> files, string folder)
        {
            var uploadedFiles = new List<string>();

            foreach (var file in files)
            {
                try
                {
                    var filePath = await UploadImageAsync(file, folder);
                    uploadedFiles.Add(filePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Failed to upload image: {file.FileName}");
                }
            }

            return uploadedFiles;
        }

        public async Task<string> UploadListingPhotoAsync(IFormFile file, int listingId)
        {
            return await UploadImageAsync(file, $"listing_photos/{listingId}");
        }

        public async Task<List<string>> UploadListingPhotosAsync(List<IFormFile> files, int listingId)
        {
            return await UploadImagesAsync(files, $"listing_photos/{listingId}");
        }

        public async Task<string> UploadUserPhotoAsync(IFormFile file, int userId)
        {
            return await UploadImageAsync(file, "user_photos");
        }

        public async Task<string> UploadBrandPhotoAsync(IFormFile file, int brandId)
        {
            return await UploadImageAsync(file, "listing_brand_photos");
        }

        public async Task<string> UploadLocationPhotoAsync(IFormFile file, int locationId)
        {
            return await UploadImageAsync(file, "listing_location_photos");
        }

        public async Task<string> UploadFileAsync(IFormFile file, string folder)
        {
            if (!IsValidFileSize(file, _maxFileSize))
                throw new ArgumentException("File size exceeds maximum allowed size");

            if (!IsValidFileAsync(file, _allowedFileExtensions).Result)
                throw new ArgumentException("Invalid file type");

            var uploadPath = GetUploadPath(folder);
            Directory.CreateDirectory(uploadPath);

            var fileName = GetUniqueFileName(file.FileName);
            var filePath = Path.Combine(uploadPath, fileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            _logger.LogInformation($"File uploaded successfully: {fileName}");
            return GetRelativePath(filePath);
        }

        public async Task<List<string>> UploadFilesAsync(List<IFormFile> files, string folder)
        {
            var uploadedFiles = new List<string>();

            foreach (var file in files)
            {
                try
                {
                    var filePath = await UploadFileAsync(file, folder);
                    uploadedFiles.Add(filePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Failed to upload file: {file.FileName}");
                }
            }

            return uploadedFiles;
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                var absolutePath = GetAbsolutePath(filePath);
                if (File.Exists(absolutePath))
                {
                    File.Delete(absolutePath);
                    _logger.LogInformation($"File deleted successfully: {filePath}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to delete file: {filePath}");
                return false;
            }
        }

        public async Task<bool> DeleteFilesAsync(List<string> filePaths)
        {
            var allDeleted = true;
            foreach (var filePath in filePaths)
            {
                if (!await DeleteFileAsync(filePath))
                    allDeleted = false;
            }
            return allDeleted;
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            var absolutePath = GetAbsolutePath(filePath);
            return File.Exists(absolutePath);
        }

        public async Task<long> GetFileSizeAsync(string filePath)
        {
            var absolutePath = GetAbsolutePath(filePath);
            if (File.Exists(absolutePath))
            {
                var fileInfo = new FileInfo(absolutePath);
                return fileInfo.Length;
            }
            return 0;
        }

        public async Task<string> ResizeImageAsync(string imagePath, int width, int height)
        {
            var absolutePath = GetAbsolutePath(imagePath);
            if (!File.Exists(absolutePath))
                throw new FileNotFoundException("Image file not found");

            var resizedFileName = $"resized_{width}x{height}_{Path.GetFileName(absolutePath)}";
            var resizedPath = Path.Combine(Path.GetDirectoryName(absolutePath)!, resizedFileName);

            using (var image = await Image.LoadAsync(absolutePath))
            {
                image.Mutate(x => x.Resize(width, height));
                await image.SaveAsync(resizedPath);
            }

            return GetRelativePath(resizedPath);
        }

        public async Task<string> CreateThumbnailAsync(string imagePath, int size = 150)
        {
            return await ResizeImageAsync(imagePath, size, size);
        }

        public async Task<bool> IsValidImageAsync(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return false;

            if (!IsValidImageExtension(file.FileName))
                return false;

            if (!IsValidFileSize(file, _maxImageSize))
                return false;

            // Check if file is actually an image by trying to load it
            try
            {
                using (var stream = file.OpenReadStream())
                {
                    using (var image = await Image.LoadAsync(stream))
                    {
                        return true;
                    }
                }
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> IsValidFileAsync(IFormFile file, string[] allowedExtensions)
        {
            if (file == null || file.Length == 0)
                return false;

            var extension = GetFileExtension(file.FileName).ToLowerInvariant();
            return allowedExtensions.Contains(extension);
        }

        public bool IsValidImageExtension(string fileName)
        {
            var extension = GetFileExtension(fileName).ToLowerInvariant();
            return _allowedImageExtensions.Contains(extension);
        }

        public bool IsValidFileSize(IFormFile file, long maxSizeInBytes)
        {
            return file.Length <= maxSizeInBytes;
        }

        public string GetUniqueFileName(string originalFileName)
        {
            var extension = GetFileExtension(originalFileName);
            var fileName = Path.GetFileNameWithoutExtension(originalFileName);
            var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
            var guid = Guid.NewGuid().ToString("N")[..8];
            
            return $"{fileName}_{timestamp}_{guid}{extension}";
        }

        public string GetFileExtension(string fileName)
        {
            return Path.GetExtension(fileName);
        }

        public string GetUploadPath(string folder)
        {
            return Path.Combine(_environment.WebRootPath, "uploads", folder);
        }

        public string GetRelativePath(string fullPath)
        {
            var webRootPath = _environment.WebRootPath;
            if (fullPath.StartsWith(webRootPath))
            {
                return fullPath.Substring(webRootPath.Length).Replace('\\', '/').TrimStart('/');
            }
            return fullPath;
        }

        public string GetAbsolutePath(string relativePath)
        {
            if (Path.IsPathRooted(relativePath))
                return relativePath;
                
            return Path.Combine(_environment.WebRootPath, relativePath.Replace('/', Path.DirectorySeparatorChar));
        }

        public void DeleteFile(string fileName, string folder)
        {
            throw new NotImplementedException();
        }
    }
}
